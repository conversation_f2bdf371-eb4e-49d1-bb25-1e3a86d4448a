// File: lib/features/settings/presentation/widgets/settings_sub_branch_item.dart
import 'package:flutter/material.dart';
import '../../domain/models/settings_sub_branch.dart';

/// A widget that displays a settings sub-branch item
class SettingsSubBranchItem extends StatelessWidget {
  /// The sub-branch to display
  final SettingsSubBranch subBranch;

  /// Callback when the item is tapped
  final VoidCallback onTap;

  /// Constructor
  const SettingsSubBranchItem({
    super.key,
    required this.subBranch,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final textColor = isDarkMode ? Colors.white : Colors.black87;
    final subtitleColor = isDarkMode ? Colors.white70 : Colors.black54;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8.0),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 8.0),
        child: Row(
          children: [
            Icon(
              subBranch.icon,
              color: subBranch.iconColor,
              size: 20,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    subBranch.title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                      color: textColor,
                    ),
                  ),
                  if (subBranch.subtitle != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      subBranch.subtitle!,
                      style: TextStyle(
                        fontSize: 12,
                        color: subtitleColor,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            if (subBranch.currentValue != null) ...[
              Text(
                subBranch.currentValue!,
                style: TextStyle(
                  fontSize: 14,
                  color: subtitleColor,
                ),
              ),
              const SizedBox(width: 8),
            ],
            Icon(
              Icons.chevron_right,
              color: subtitleColor,
              size: 20,
            ),
          ],
        ),
      ),
    );
  }
}
