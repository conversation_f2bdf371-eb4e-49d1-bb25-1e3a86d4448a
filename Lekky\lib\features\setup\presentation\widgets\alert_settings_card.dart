import 'package:flutter/material.dart';
import '../../../../core/shared/widgets/currency_input_field.dart';
import '../../../../core/shared/widgets/integer_input_field.dart';
import '../../../../generated/l10n/app_localizations.dart';
import 'setup_section_header.dart';
import 'info_notice.dart';

/// A widget for alert settings in the setup screen
class AlertSettingsCard extends StatelessWidget {
  /// Current alert threshold
  final double alertThreshold;

  /// Current days in advance
  final int daysInAdvance;

  /// Currency symbol
  final String currencySymbol;

  /// Callback when alert threshold changes
  final Function(double) onAlertThresholdChanged;

  /// Callback when days in advance changes
  final Function(int) onDaysInAdvanceChanged;

  /// Constructor
  const AlertSettingsCard({
    super.key,
    required this.alertThreshold,
    required this.daysInAdvance,
    required this.currencySymbol,
    required this.onAlertThresholdChanged,
    required this.onDaysInAdvanceChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SetupSectionHeader(
              title: AppLocalizations.of(context).setupAlertSettings,
              description: AppLocalizations.of(context).setupAlertSettingsDesc,
              icon: Icons.notifications,
            ),

            // Alert Threshold Subsection
            Text(
              AppLocalizations.of(context).alertThreshold,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              AppLocalizations.of(context).alertThresholdDesc,
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 8),

            CurrencyInputField(
              value: alertThreshold,
              onChanged: (value) =>
                  value != null ? onAlertThresholdChanged(value) : null,
              currencySymbol: currencySymbol,
              labelText: 'Alert Threshold',
              hintText: 'Enter amount',
              minValue: 1.00,
              maxValue: 999.99,
              borderRadius: BorderRadius.circular(8),
            ),

            const SizedBox(height: 8),

            const InfoNotice(
              message:
                  'Low balance alerts will be active after you enter your first meter reading.',
              icon: Icons.info_outline,
            ),

            const SizedBox(height: 8),

            Text(
              'Tip: Set this to the amount you typically top up with to get reminders at the right time.',
              style: TextStyle(
                fontSize: 12,
                fontStyle: FontStyle.italic,
                color: Theme.of(context).textTheme.bodySmall?.color,
              ),
            ),

            const SizedBox(height: 24),

            // Days in Advance Subsection
            Text(
              AppLocalizations.of(context).daysInAdvance,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              AppLocalizations.of(context).daysInAdvanceDesc,
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 8),

            IntegerInputField(
              value: daysInAdvance,
              onChanged: (value) =>
                  value != null ? onDaysInAdvanceChanged(value) : null,
              suffixText: 'days',
              labelText: 'Days in Advance',
              hintText: 'Enter days',
              minValue: 0,
              maxValue: 99,
              borderRadius: BorderRadius.circular(8),
            ),

            const SizedBox(height: 8),

            const InfoNotice(
              message:
                  'Days in advance alerts will be active after you enter at least two meter readings to calculate your average usage.',
              icon: Icons.info_outline,
            ),

            const SizedBox(height: 8),

            Text(
              'Tip: Consider your usage patterns when setting this value. If you use electricity quickly, choose fewer days.',
              style: TextStyle(
                fontSize: 12,
                fontStyle: FontStyle.italic,
                color: Theme.of(context).textTheme.bodySmall?.color,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
