import 'app_localizations.dart';

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'Lekky';

  @override
  String get tagline => 'Your Prepaid Meter Assistant';

  @override
  String get splashQuote => 'I\'m not cheap—I\'m kilowatt-conscious.';

  @override
  String get checkingPermissions => 'Checking permissions...';

  @override
  String get initializing => 'Initializing...';

  @override
  String get welcomeTitle => 'Welcome to Lekky';

  @override
  String get welcomeSubtitle => 'Your personal prepaid meter assistant';

  @override
  String get trackUsage => 'Track Your Usage';

  @override
  String get getAlerts => 'Get Timely Alerts';

  @override
  String get viewHistory => 'View History';

  @override
  String get calculateCosts => 'Calculate Costs';

  @override
  String get trackUsageDesc => 'Monitor your electricity consumption and spending';

  @override
  String get getAlertsDesc => 'Receive notifications when your balance is running low';

  @override
  String get viewHistoryDesc => 'See your past meter readings and top-ups';

  @override
  String get calculateCostsDesc => 'Estimate your electricity costs over different periods';

  @override
  String get getStarted => 'Get Started';

  @override
  String get restoreData => 'Restore Previous Data';

  @override
  String get restoreHelper => 'Have a backup from another device?';

  @override
  String get restoreDataTitle => 'Restore Data';

  @override
  String get restoreDataContent => 'This feature will allow you to restore data from a backup file.';

  @override
  String get cancel => 'Cancel';

  @override
  String get chooseFile => 'Choose File';

  @override
  String get regionSettings => 'Region Settings';

  @override
  String get language => 'Language';

  @override
  String get currency => 'Currency';

  @override
  String get selectLanguage => 'Select your preferred language for the app interface.';

  @override
  String get selectCurrency => 'Select the currency for your meter readings.';

  @override
  String get currencyTip => 'Tip: Select the currency that matches your electricity bills.';

  @override
  String get perDay => '/day';

  @override
  String get dashboard => 'Dashboard';

  @override
  String get history => 'History';

  @override
  String get settings => 'Settings';

  @override
  String get noEntriesFound => 'No entries found';

  @override
  String get tryAdjustingFilters => 'Try adjusting your filters to see more entries';

  @override
  String get noEntriesYet => 'No entries yet';

  @override
  String get addFirstEntry => 'Add your first meter reading or top-up to get started';

  @override
  String get errorLoadingData => 'Error loading data';

  @override
  String errorLoadingPreferences(String error) {
    return 'Error loading preferences: $error';
  }

  @override
  String get meterReading => 'Meter Reading';

  @override
  String get topUp => 'Top Up';

  @override
  String get lastUpdated => 'Last Updated';

  @override
  String get daysRemaining => 'Days Remaining';

  @override
  String get currentBalance => 'Current Balance';

  @override
  String get usageStatistics => 'Usage Statistics';

  @override
  String get recentAverage => 'Recent Average';

  @override
  String get totalAverage => 'Total Average';

  @override
  String get dailyUsage => 'Daily Usage';

  @override
  String get topUpStatistics => 'Top Up Statistics';

  @override
  String get daysToAlert => 'Days to Alert';

  @override
  String get daysToZero => 'Days to Zero';

  @override
  String get quickActions => 'Quick Actions';

  @override
  String get addEntry => 'Add Entry';

  @override
  String get recentActivity => 'Recent Activity';

  @override
  String get viewAll => 'View All';

  @override
  String get save => 'Save';

  @override
  String get delete => 'Delete';

  @override
  String get edit => 'Edit';

  @override
  String get add => 'Add';

  @override
  String get close => 'Close';

  @override
  String get ok => 'OK';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get loading => 'Loading...';

  @override
  String get saving => 'Saving...';

  @override
  String get region => 'Region';

  @override
  String get languageCurrency => 'Language, Currency';

  @override
  String get recentAvgUsage => 'Recent-avg shows usage between consecutive readings';

  @override
  String get tapNotificationBell => 'Tap the notification bell icon to view all notifications';

  @override
  String get addReadingsRegularly => 'Add new meter readings regularly for better usage statistics';

  @override
  String get setupAlertsLowBalance => 'Set up alerts to be notified when your balance is low';

  @override
  String get useQuickActions => 'Use the Quick Actions to add new readings or top-ups';

  @override
  String get viewHistoryTip => 'View your history to see all past meter readings and top-ups';

  @override
  String get notificationsGrouped => 'Notifications are grouped by type for easy organization';

  @override
  String get swipeNotifications => 'Swipe left on notifications to mark as read, right to delete';

  @override
  String get configureThresholds => 'Configure notification thresholds in Settings > Alerts & Notifications';

  @override
  String get lowBalanceHelp => 'Low balance alerts help you avoid running out of credit';

  @override
  String get daysInAdvanceTip => 'Set \\\"Days in Advance\\\" to get top-up reminders early';

  @override
  String get today => 'Today';

  @override
  String get yesterday => 'Yesterday';

  @override
  String get lastWeek => 'Last week';

  @override
  String get lastMonth => 'Last month';

  @override
  String get never => 'Never';

  @override
  String get days => 'days';

  @override
  String get day => 'day';

  @override
  String get hours => 'hours';

  @override
  String get hour => 'hour';

  @override
  String get minutes => 'minutes';

  @override
  String get minute => 'minute';

  @override
  String get retry => 'Retry';

  @override
  String get skip => 'Skip';

  @override
  String get complete => 'Complete';

  @override
  String get failed => 'Failed';

  @override
  String get syncing => 'Syncing...';

  @override
  String get deleting => 'Deleting...';

  @override
  String get noMeterReading => 'No meter reading available';

  @override
  String get addFirstReading => 'Add your first reading';

  @override
  String get nextTopUp => 'Next top-up';

  @override
  String get addReading => 'Add reading';

  @override
  String get addTopUp => 'Add top-up';

  @override
  String get noRecentActivity => 'No recent activity';

  @override
  String get invalidEntry => 'Invalid entry';

  @override
  String get missingData => 'Missing data';

  @override
  String get dataInconsistency => 'Data inconsistency';

  @override
  String get validationError => 'Validation error';

  @override
  String get failedToSave => 'Failed to save';

  @override
  String get networkError => 'Network error';

  @override
  String get permissionDenied => 'Permission denied';

  @override
  String get fileNotFound => 'File not found';

  @override
  String get invalidFileFormat => 'Invalid file format';

  @override
  String get addEntryDialog => 'Add Entry';

  @override
  String get editEntry => 'Edit Entry';

  @override
  String get deleteEntry => 'Delete Entry';

  @override
  String get confirmDelete => 'Confirm Delete';

  @override
  String get exportData => 'Export Data';

  @override
  String get importData => 'Import Data';

  @override
  String get settingsDialog => 'Settings';

  @override
  String get about => 'About';

  @override
  String get lowBalanceAlert => 'Low balance alert';

  @override
  String get timeToTopUp => 'Time to top up';

  @override
  String get meterReadingReminder => 'Meter reading reminder';

  @override
  String get dataBackupReminder => 'Data backup reminder';

  @override
  String get alertsNotifications => 'Alerts & Notifications';

  @override
  String get dateTime => 'Date & Time';

  @override
  String get theme => 'Theme';

  @override
  String get dataManagement => 'Data Management';

  @override
  String get appInformation => 'App Information';

  @override
  String get setup => 'Setup';

  @override
  String get setupRegionSettings => 'Region Settings';

  @override
  String get setupRegionSettingsDesc => 'Configure language and currency preferences.';

  @override
  String get setupInitialMeterReading => 'Initial Meter Reading';

  @override
  String get setupInitialMeterReadingDesc => 'Enter your current meter reading to start tracking.';

  @override
  String get setupAlertSettings => 'Alert Settings';

  @override
  String get setupAlertSettingsDesc => 'Configure when you want to receive alerts about your meter balance.';

  @override
  String get setupDateSettings => 'Date Settings';

  @override
  String get setupDateSettingsDesc => 'Configure how dates are displayed in the app.';

  @override
  String get setupAppearance => 'Appearance';

  @override
  String get setupAppearanceDesc => 'Customize the look and feel of the app.';

  @override
  String get finishSetup => 'Finish Setup';

  @override
  String setupFailed(String error) {
    return 'Setup failed: $error';
  }

  @override
  String get pleaseCheckInputs => 'Please check your inputs.';

  @override
  String get dateSettingsTitle => 'Date Settings';

  @override
  String get dateSettingsDesc => 'Choose how dates will be displayed throughout the app.';

  @override
  String get dateFormat => 'Date Format';

  @override
  String get alertThreshold => 'Alert Threshold';

  @override
  String get alertThresholdDesc => 'You will be notified when your balance falls below this amount.';

  @override
  String get daysInAdvance => 'Days in Advance';

  @override
  String get daysInAdvanceDesc => 'How many days before running out of credit to send reminders.';

  @override
  String get initialMeterReadingOptional => 'This is optional. You can skip this step and add your first meter reading later.';

  @override
  String errorLoadingSettings(String error) {
    return 'Error loading settings: $error';
  }
}
