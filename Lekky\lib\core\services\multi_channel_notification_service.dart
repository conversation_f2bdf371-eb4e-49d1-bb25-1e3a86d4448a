import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import '../utils/logger.dart';
import '../../features/notifications/domain/models/notification.dart';
import '../../features/notifications/data/notification_service.dart';

/// Enum for different notification delivery channels
enum NotificationChannel {
  localNotification,
  email,
  sms,
  webPush,
  calendar,
}

/// Configuration for multi-channel notification delivery
class NotificationChannelConfig {
  final NotificationChannel channel;
  final bool isEnabled;
  final Map<String, dynamic> settings;

  const NotificationChannelConfig({
    required this.channel,
    required this.isEnabled,
    this.settings = const {},
  });
}

/// Service for delivering notifications through multiple channels
class MultiChannelNotificationService {
  static final MultiChannelNotificationService _instance =
      MultiChannelNotificationService._internal();

  factory MultiChannelNotificationService() => _instance;

  NotificationService? _localNotificationService;
  static const String _channelConfigKey = 'multi_channel_config';

  MultiChannelNotificationService._internal();

  /// Initialize multi-channel notification service
  Future<void> initialize() async {
    try {
      Logger.info('MultiChannelNotificationService: Initializing');

      // Load channel configurations
      await _loadChannelConfigurations();

      Logger.info('MultiChannelNotificationService: Initialization complete');
    } catch (e) {
      Logger.error(
          'MultiChannelNotificationService: Initialization failed: $e');
    }
  }

  /// Send notification through all enabled channels
  Future<Map<NotificationChannel, bool>> sendMultiChannelNotification(
    AppNotification notification,
    DateTime scheduledDate, {
    List<NotificationChannel>? specificChannels,
  }) async {
    final results = <NotificationChannel, bool>{};
    final configs = await _getChannelConfigurations();

    final channelsToUse = specificChannels ??
        configs.where((c) => c.isEnabled).map((c) => c.channel).toList();

    for (final channel in channelsToUse) {
      try {
        final success = await _sendThroughChannel(
          channel,
          notification,
          scheduledDate,
        );
        results[channel] = success;

        Logger.info('Notification sent through $channel: $success');
      } catch (e) {
        Logger.error('Failed to send notification through $channel: $e');
        results[channel] = false;
      }
    }

    return results;
  }

  /// Send notification through specific channel
  Future<bool> _sendThroughChannel(
    NotificationChannel channel,
    AppNotification notification,
    DateTime scheduledDate,
  ) async {
    switch (channel) {
      case NotificationChannel.localNotification:
        return await _sendLocalNotification(notification, scheduledDate);
      case NotificationChannel.email:
        return await _sendEmailNotification(notification, scheduledDate);
      case NotificationChannel.sms:
        return await _sendSMSNotification(notification, scheduledDate);
      case NotificationChannel.webPush:
        return await _sendWebPushNotification(notification, scheduledDate);
      case NotificationChannel.calendar:
        return await _sendCalendarNotification(notification, scheduledDate);
    }
  }

  /// Send local notification
  Future<bool> _sendLocalNotification(
    AppNotification notification,
    DateTime scheduledDate,
  ) async {
    try {
      if (_localNotificationService == null) {
        Logger.error('Local notification service not initialized');
        return false;
      }

      await _localNotificationService!.scheduleNotification(
        notification,
        scheduledDate,
      );
      return true;
    } catch (e) {
      Logger.error('Failed to send local notification: $e');
      return false;
    }
  }

  /// Send email notification (placeholder implementation)
  Future<bool> _sendEmailNotification(
    AppNotification notification,
    DateTime scheduledDate,
  ) async {
    try {
      final configs = await _getChannelConfigurations();
      final emailConfig = configs.firstWhere(
        (c) => c.channel == NotificationChannel.email,
        orElse: () => const NotificationChannelConfig(
          channel: NotificationChannel.email,
          isEnabled: false,
        ),
      );

      if (!emailConfig.isEnabled) return false;

      final emailAddress = emailConfig.settings['emailAddress'] as String?;
      if (emailAddress == null || emailAddress.isEmpty) return false;

      // Create email content
      final subject = Uri.encodeComponent('Lekky: ${notification.title}');
      final body = Uri.encodeComponent(
        '${notification.message}\n\n'
        'Scheduled for: ${scheduledDate.toString()}\n'
        'Sent from Lekky Meter Tracking App',
      );

      final emailUri =
          Uri.parse('mailto:$emailAddress?subject=$subject&body=$body');

      // This would open the default email app
      // In production, you might want to use a proper email service
      if (await canLaunchUrl(emailUri)) {
        await launchUrl(emailUri);
        return true;
      }

      return false;
    } catch (e) {
      Logger.error('Failed to send email notification: $e');
      return false;
    }
  }

  /// Send SMS notification (placeholder implementation)
  Future<bool> _sendSMSNotification(
    AppNotification notification,
    DateTime scheduledDate,
  ) async {
    try {
      final configs = await _getChannelConfigurations();
      final smsConfig = configs.firstWhere(
        (c) => c.channel == NotificationChannel.sms,
        orElse: () => const NotificationChannelConfig(
          channel: NotificationChannel.sms,
          isEnabled: false,
        ),
      );

      if (!smsConfig.isEnabled) return false;

      final phoneNumber = smsConfig.settings['phoneNumber'] as String?;
      if (phoneNumber == null || phoneNumber.isEmpty) return false;

      // Create SMS content
      final message = Uri.encodeComponent(
        'Lekky: ${notification.title}\n${notification.message}',
      );

      final smsUri = Uri.parse('sms:$phoneNumber?body=$message');

      // This would open the default SMS app
      if (await canLaunchUrl(smsUri)) {
        await launchUrl(smsUri);
        return true;
      }

      return false;
    } catch (e) {
      Logger.error('Failed to send SMS notification: $e');
      return false;
    }
  }

  /// Send web push notification (placeholder implementation)
  Future<bool> _sendWebPushNotification(
    AppNotification notification,
    DateTime scheduledDate,
  ) async {
    try {
      // This would integrate with a web push service like Firebase Cloud Messaging
      // For now, it's a placeholder
      Logger.info('Web push notification would be sent: ${notification.title}');
      return true;
    } catch (e) {
      Logger.error('Failed to send web push notification: $e');
      return false;
    }
  }

  /// Send calendar notification (placeholder implementation)
  Future<bool> _sendCalendarNotification(
    AppNotification notification,
    DateTime scheduledDate,
  ) async {
    try {
      // Create calendar event
      final title = Uri.encodeComponent(notification.title);
      final details = Uri.encodeComponent(notification.message);
      final startDate =
          scheduledDate.toUtc().toIso8601String().replaceAll(':', '%3A');
      final endDate = scheduledDate
          .add(const Duration(minutes: 15))
          .toUtc()
          .toIso8601String()
          .replaceAll(':', '%3A');

      // Google Calendar URL
      final calendarUri = Uri.parse(
        'https://calendar.google.com/calendar/render?action=TEMPLATE'
        '&text=$title'
        '&dates=$startDate/$endDate'
        '&details=$details',
      );

      if (await canLaunchUrl(calendarUri)) {
        await launchUrl(calendarUri);
        return true;
      }

      return false;
    } catch (e) {
      Logger.error('Failed to send calendar notification: $e');
      return false;
    }
  }

  /// Configure notification channels
  Future<void> configureChannels(
      List<NotificationChannelConfig> configs) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final configData = configs
          .map((c) => {
                'channel': c.channel.index,
                'isEnabled': c.isEnabled,
                'settings': c.settings,
              })
          .toList();

      await prefs.setString(_channelConfigKey, _encodeConfigs(configData));
      Logger.info('Notification channel configurations saved');
    } catch (e) {
      Logger.error('Failed to save channel configurations: $e');
    }
  }

  /// Get current channel configurations
  Future<List<NotificationChannelConfig>> _getChannelConfigurations() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final configJson = prefs.getString(_channelConfigKey);

      if (configJson != null) {
        final configData = _decodeConfigs(configJson);
        return configData
            .map((data) => NotificationChannelConfig(
                  channel: NotificationChannel.values[data['channel'] as int],
                  isEnabled: data['isEnabled'] as bool,
                  settings: Map<String, dynamic>.from(data['settings'] as Map),
                ))
            .toList();
      }

      return _getDefaultConfigurations();
    } catch (e) {
      Logger.error('Failed to load channel configurations: $e');
      return _getDefaultConfigurations();
    }
  }

  /// Load channel configurations
  Future<void> _loadChannelConfigurations() async {
    try {
      final configs = await _getChannelConfigurations();
      Logger.info('Loaded ${configs.length} channel configurations');
    } catch (e) {
      Logger.error('Failed to load channel configurations: $e');
    }
  }

  /// Get default channel configurations
  List<NotificationChannelConfig> _getDefaultConfigurations() {
    return [
      const NotificationChannelConfig(
        channel: NotificationChannel.localNotification,
        isEnabled: true,
      ),
      const NotificationChannelConfig(
        channel: NotificationChannel.email,
        isEnabled: false,
        settings: {'emailAddress': ''},
      ),
      const NotificationChannelConfig(
        channel: NotificationChannel.sms,
        isEnabled: false,
        settings: {'phoneNumber': ''},
      ),
      const NotificationChannelConfig(
        channel: NotificationChannel.webPush,
        isEnabled: false,
      ),
      const NotificationChannelConfig(
        channel: NotificationChannel.calendar,
        isEnabled: false,
      ),
    ];
  }

  /// Get delivery statistics
  Future<Map<String, dynamic>> getDeliveryStatistics() async {
    try {
      // This would track delivery success rates across channels
      // For now, return placeholder data
      return {
        'totalNotificationsSent': 0,
        'successRateByChannel': {
          'localNotification': 0.95,
          'email': 0.80,
          'sms': 0.85,
          'webPush': 0.90,
          'calendar': 0.75,
        },
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      Logger.error('Failed to get delivery statistics: $e');
      return {'error': e.toString()};
    }
  }

  /// Simple encoding for configurations (use proper JSON in production)
  String _encodeConfigs(List<Map<String, dynamic>> configs) {
    return configs
        .map((c) => c.entries.map((e) => '${e.key}:${e.value}').join(','))
        .join('|');
  }

  /// Simple decoding for configurations (use proper JSON in production)
  List<Map<String, dynamic>> _decodeConfigs(String encoded) {
    return encoded.split('|').map((configStr) {
      final config = <String, dynamic>{};
      for (final pair in configStr.split(',')) {
        final keyValue = pair.split(':');
        if (keyValue.length == 2) {
          final key = keyValue[0];
          final value = keyValue[1];

          switch (key) {
            case 'channel':
              config[key] = int.tryParse(value) ?? 0;
              break;
            case 'isEnabled':
              config[key] = value == 'true';
              break;
            case 'settings':
              config[key] = <String, dynamic>{}; // Simplified
              break;
            default:
              config[key] = value;
          }
        }
      }
      return config;
    }).toList();
  }
}
