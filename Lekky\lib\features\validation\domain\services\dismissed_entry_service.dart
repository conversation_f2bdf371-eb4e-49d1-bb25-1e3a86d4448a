// File: lib/features/validation/domain/services/dismissed_entry_service.dart
import '../../../../core/utils/logger.dart';
import '../../../../core/shared/enums/entry_enums.dart';
import '../../../../core/di/service_locator.dart';
import '../../../../core/utils/event_bus.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../meter_readings/domain/repositories/meter_reading_repository.dart';

import '../../../top_ups/domain/repositories/top_up_repository.dart';
import '../models/validation_issue.dart';
import 'simple_gap_detection_service.dart';
import 'validation_trigger_service.dart';

/// Simple service for managing dismissed gaps and duplicate entries
class DismissedEntryService {
  final SimpleGapDetectionService _gapDetectionService;

  const DismissedEntryService(this._gapDetectionService);

  /// Dismiss a missing entry gap by creating a Records Gap entry
  Future<void> createDismissalEntry(ValidationIssue issue) async {
    try {
      Logger.info(
          'DismissedEntryService: Creating Records Gap entry for dismissed gap');
      await _gapDetectionService.dismissGap(issue);
      Logger.info(
          'DismissedEntryService: Records Gap entry created successfully');
    } catch (e) {
      Logger.error(
          'DismissedEntryService: Error creating Records Gap entry: $e');
      rethrow;
    }
  }

  /// Dismiss a duplicate entry by marking it as ignored
  Future<void> dismissDuplicateEntry(ValidationIssue issue) async {
    try {
      final entryId = issue.entryId;
      if (entryId == null) {
        throw Exception('Cannot dismiss duplicate entry: no entry ID provided');
      }

      Logger.info(
          'DismissedEntryService: Dismissing duplicate entry with ID $entryId');

      // Determine if this is a meter reading or top-up based on metadata
      final metadata = issue.metadata ?? {};
      final originalDate = DateTime.parse(metadata['date'] as String);

      // Try to get the entry from meter readings first
      final meterReadingRepo = serviceLocator<MeterReadingRepository>();
      final meterReading = await meterReadingRepo.getMeterReadingById(entryId);

      if (meterReading != null) {
        // This is a meter reading - mark it as dismissed
        final dismissedReading = meterReading.copyWith(
          status: EntryStatus.ignored,
          notes: _buildDuplicateDismissalNotes(
            originalDate,
            meterReading.notes,
            'meter reading',
          ),
        );

        await meterReadingRepo.updateMeterReading(dismissedReading);
        Logger.info(
            'DismissedEntryService: Successfully dismissed duplicate meter reading');

        // Trigger validation and fire events for meter reading
        _triggerValidationAfterDismissal(entryId, originalDate, true);
      } else {
        // Try top-ups
        final topUpRepo = serviceLocator<TopUpRepository>();
        final topUp = await topUpRepo.getTopUpById(entryId);

        if (topUp != null) {
          // This is a top-up - mark it as dismissed
          final dismissedTopUp = topUp.copyWith(
            status: EntryStatus.ignored,
            notes: _buildDuplicateDismissalNotes(
              originalDate,
              topUp.notes,
              'top-up',
            ),
          );

          await topUpRepo.updateTopUp(dismissedTopUp);
          Logger.info(
              'DismissedEntryService: Successfully dismissed duplicate top-up');

          // Trigger validation and fire events for top-up
          _triggerValidationAfterDismissal(entryId, originalDate, false);
        } else {
          throw Exception('Entry with ID $entryId not found in any repository');
        }
      }
    } catch (e) {
      Logger.error(
          'DismissedEntryService: Error dismissing duplicate entry: $e');
      rethrow;
    }
  }

  /// Build notes for dismissed duplicate entries
  String _buildDuplicateDismissalNotes(
    DateTime originalDate,
    String? existingNotes,
    String entryType,
  ) {
    final dateStr = '${originalDate.day.toString().padLeft(2, '0')}/'
        '${originalDate.month.toString().padLeft(2, '0')}/'
        '${originalDate.year}';

    final dismissalNote =
        'Duplicate Entry Dismissed: Multiple ${entryType}s on $dateStr';

    if (existingNotes?.isNotEmpty == true) {
      return '$dismissalNote - $existingNotes';
    } else {
      return dismissalNote;
    }
  }

  /// Trigger validation and fire events after dismissal
  void _triggerValidationAfterDismissal(
    int entryId,
    DateTime entryDate,
    bool isMeterReading,
  ) {
    try {
      // Fire data updated event for immediate UI refresh
      EventBus().fire(EventType.dataUpdated);

      // Trigger validation through ValidationTriggerService
      final validationTrigger = serviceLocator<ValidationTriggerService>();

      if (isMeterReading) {
        validationTrigger.validateAfterUpdate(entryId).catchError((error) {
          Logger.error(
              'Failed to trigger validation after meter reading dismissal: $error');
        });
      } else {
        validationTrigger.validateAfterTopUpUpdate(entryId).catchError((error) {
          Logger.error(
              'Failed to trigger validation after top-up dismissal: $error');
        });
      }

      Logger.info(
          'DismissedEntryService: Triggered validation and events after dismissal');
    } catch (e) {
      Logger.error(
          'DismissedEntryService: Error triggering validation after dismissal: $e');
    }
  }

  /// Check if a gap period has been dismissed by looking for record gap entries
  Future<bool> isGapDismissed(
      List<dynamic> readings, DateTime start, DateTime end) async {
    try {
      // Check if any record gap entry exists within the gap period
      for (final reading in readings) {
        if (reading is MeterReading) {
          if (reading.status == EntryStatus.ignored &&
              reading.notes?.contains('Records Gap:') == true &&
              reading.date.isAfter(start) &&
              reading.date.isBefore(end)) {
            return true;
          }
        }
      }
      return false;
    } catch (e) {
      Logger.error(
          'DismissedEntryService: Error checking if gap is dismissed: $e');
      return false;
    }
  }

  /// Check if an entry is a dismissed entry (legacy method - always returns false now)
  bool isDismissedEntry(dynamic reading) {
    // No more dismissed entries in the database - they're stored as gap metadata
    return false;
  }

  /// Clean up invalid dismissal entries (legacy method - does nothing now)
  Future<void> cleanupInvalidDismissalEntries() async {
    Logger.info('DismissedEntryService: No cleanup needed with new system');
    // The migration already cleaned up old dismissed entries
  }
}
