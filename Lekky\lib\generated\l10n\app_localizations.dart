import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_de.dart';
import 'app_localizations_en.dart';
import 'app_localizations_es.dart';
import 'app_localizations_fr.dart';
import 'app_localizations_hi.dart';
import 'app_localizations_pt.dart';
import 'app_localizations_zh.dart';

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('ar'),
    Locale('de'),
    Locale('es'),
    Locale('fr'),
    Locale('hi'),
    Locale('pt'),
    Locale('zh')
  ];

  /// The name of the application
  ///
  /// In en, this message translates to:
  /// **'Lekky'**
  String get appName;

  /// App tagline displayed on splash screen
  ///
  /// In en, this message translates to:
  /// **'Your Prepaid Meter Assistant'**
  String get tagline;

  /// Humorous quote displayed on splash screen
  ///
  /// In en, this message translates to:
  /// **'I\'m not cheap—I\'m kilowatt-conscious.'**
  String get splashQuote;

  /// Status message while checking app permissions
  ///
  /// In en, this message translates to:
  /// **'Checking permissions...'**
  String get checkingPermissions;

  /// Status message during app initialization
  ///
  /// In en, this message translates to:
  /// **'Initializing...'**
  String get initializing;

  /// Main title on welcome screen
  ///
  /// In en, this message translates to:
  /// **'Welcome to Lekky'**
  String get welcomeTitle;

  /// Subtitle on welcome screen
  ///
  /// In en, this message translates to:
  /// **'Your personal prepaid meter assistant'**
  String get welcomeSubtitle;

  /// Feature title for usage tracking
  ///
  /// In en, this message translates to:
  /// **'Track Your Usage'**
  String get trackUsage;

  /// Feature title for alert notifications
  ///
  /// In en, this message translates to:
  /// **'Get Timely Alerts'**
  String get getAlerts;

  /// Feature title for viewing history
  ///
  /// In en, this message translates to:
  /// **'View History'**
  String get viewHistory;

  /// Feature title for cost calculation
  ///
  /// In en, this message translates to:
  /// **'Calculate Costs'**
  String get calculateCosts;

  /// Description for usage tracking feature
  ///
  /// In en, this message translates to:
  /// **'Monitor your electricity consumption and spending'**
  String get trackUsageDesc;

  /// Description for alert notifications feature
  ///
  /// In en, this message translates to:
  /// **'Receive notifications when your balance is running low'**
  String get getAlertsDesc;

  /// Description for history viewing feature
  ///
  /// In en, this message translates to:
  /// **'See your past meter readings and top-ups'**
  String get viewHistoryDesc;

  /// Description for cost calculation feature
  ///
  /// In en, this message translates to:
  /// **'Estimate your electricity costs over different periods'**
  String get calculateCostsDesc;

  /// Button text to begin app setup
  ///
  /// In en, this message translates to:
  /// **'Get Started'**
  String get getStarted;

  /// Button text for data restoration option
  ///
  /// In en, this message translates to:
  /// **'Restore Previous Data'**
  String get restoreData;

  /// Helper text for data restoration
  ///
  /// In en, this message translates to:
  /// **'Have a backup from another device?'**
  String get restoreHelper;

  /// Dialog title for data restoration
  ///
  /// In en, this message translates to:
  /// **'Restore Data'**
  String get restoreDataTitle;

  /// Dialog content explaining data restoration
  ///
  /// In en, this message translates to:
  /// **'This feature will allow you to restore data from a backup file.'**
  String get restoreDataContent;

  /// Generic cancel button text
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// Button text for file selection
  ///
  /// In en, this message translates to:
  /// **'Choose File'**
  String get chooseFile;

  /// Title for regional settings section
  ///
  /// In en, this message translates to:
  /// **'Region Settings'**
  String get regionSettings;

  /// Label for language setting
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// Label for currency setting
  ///
  /// In en, this message translates to:
  /// **'Currency'**
  String get currency;

  /// Instruction for language selection
  ///
  /// In en, this message translates to:
  /// **'Select your preferred language for the app interface.'**
  String get selectLanguage;

  /// Instruction for currency selection
  ///
  /// In en, this message translates to:
  /// **'Select the currency for your meter readings.'**
  String get selectCurrency;

  /// Helpful tip for currency selection
  ///
  /// In en, this message translates to:
  /// **'Tip: Select the currency that matches your electricity bills.'**
  String get currencyTip;

  /// Suffix for daily rate display
  ///
  /// In en, this message translates to:
  /// **'/day'**
  String get perDay;

  /// Dashboard screen title
  ///
  /// In en, this message translates to:
  /// **'Dashboard'**
  String get dashboard;

  /// History screen title
  ///
  /// In en, this message translates to:
  /// **'History'**
  String get history;

  /// Settings screen title
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// Message when no history entries match filters
  ///
  /// In en, this message translates to:
  /// **'No entries found'**
  String get noEntriesFound;

  /// Suggestion when no entries found with filters
  ///
  /// In en, this message translates to:
  /// **'Try adjusting your filters to see more entries'**
  String get tryAdjustingFilters;

  /// Message when no history entries exist
  ///
  /// In en, this message translates to:
  /// **'No entries yet'**
  String get noEntriesYet;

  /// Instruction for first-time users
  ///
  /// In en, this message translates to:
  /// **'Add your first meter reading or top-up to get started'**
  String get addFirstEntry;

  /// Generic error message for data loading failures
  ///
  /// In en, this message translates to:
  /// **'Error loading data'**
  String get errorLoadingData;

  /// Error message when preferences fail to load
  ///
  /// In en, this message translates to:
  /// **'Error loading preferences: {error}'**
  String errorLoadingPreferences(String error);

  /// Label for meter reading entries
  ///
  /// In en, this message translates to:
  /// **'Meter Reading'**
  String get meterReading;

  /// Label for top-up entries
  ///
  /// In en, this message translates to:
  /// **'Top Up'**
  String get topUp;

  /// Label for last update timestamp
  ///
  /// In en, this message translates to:
  /// **'Last Updated'**
  String get lastUpdated;

  /// Label for estimated days until meter runs out
  ///
  /// In en, this message translates to:
  /// **'Days Remaining'**
  String get daysRemaining;

  /// Label for current meter balance
  ///
  /// In en, this message translates to:
  /// **'Current Balance'**
  String get currentBalance;

  /// Title for usage statistics card
  ///
  /// In en, this message translates to:
  /// **'Usage Statistics'**
  String get usageStatistics;

  /// Label for recent average usage
  ///
  /// In en, this message translates to:
  /// **'Recent Average'**
  String get recentAverage;

  /// Label for total average usage
  ///
  /// In en, this message translates to:
  /// **'Total Average'**
  String get totalAverage;

  /// Label for daily usage amount
  ///
  /// In en, this message translates to:
  /// **'Daily Usage'**
  String get dailyUsage;

  /// Title for top-up statistics card
  ///
  /// In en, this message translates to:
  /// **'Top Up Statistics'**
  String get topUpStatistics;

  /// Label for days until alert threshold
  ///
  /// In en, this message translates to:
  /// **'Days to Alert'**
  String get daysToAlert;

  /// Label for days until meter reaches zero
  ///
  /// In en, this message translates to:
  /// **'Days to Zero'**
  String get daysToZero;

  /// Title for quick actions card
  ///
  /// In en, this message translates to:
  /// **'Quick Actions'**
  String get quickActions;

  /// Button text to add new entry
  ///
  /// In en, this message translates to:
  /// **'Add Entry'**
  String get addEntry;

  /// Title for recent activity card
  ///
  /// In en, this message translates to:
  /// **'Recent Activity'**
  String get recentActivity;

  /// Button text to view all items
  ///
  /// In en, this message translates to:
  /// **'View All'**
  String get viewAll;

  /// Generic save button text
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// Generic delete button text
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// Generic edit button text
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// Generic add button text
  ///
  /// In en, this message translates to:
  /// **'Add'**
  String get add;

  /// Generic close button text
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;

  /// Generic OK button text
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// Generic yes button text
  ///
  /// In en, this message translates to:
  /// **'Yes'**
  String get yes;

  /// Generic no button text
  ///
  /// In en, this message translates to:
  /// **'No'**
  String get no;

  /// Generic loading message
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get loading;

  /// Generic saving message
  ///
  /// In en, this message translates to:
  /// **'Saving...'**
  String get saving;

  /// Settings category for regional settings
  ///
  /// In en, this message translates to:
  /// **'Region'**
  String get region;

  /// Description for region settings category
  ///
  /// In en, this message translates to:
  /// **'Language, Currency'**
  String get languageCurrency;

  /// Description of recent average usage calculation
  ///
  /// In en, this message translates to:
  /// **'Recent-avg shows usage between consecutive readings'**
  String get recentAvgUsage;

  /// Tip about notification bell functionality
  ///
  /// In en, this message translates to:
  /// **'Tap the notification bell icon to view all notifications'**
  String get tapNotificationBell;

  /// Tip about adding regular readings
  ///
  /// In en, this message translates to:
  /// **'Add new meter readings regularly for better usage statistics'**
  String get addReadingsRegularly;

  /// Tip about setting up low balance alerts
  ///
  /// In en, this message translates to:
  /// **'Set up alerts to be notified when your balance is low'**
  String get setupAlertsLowBalance;

  /// Tip about using quick actions
  ///
  /// In en, this message translates to:
  /// **'Use the Quick Actions to add new readings or top-ups'**
  String get useQuickActions;

  /// Tip about viewing history
  ///
  /// In en, this message translates to:
  /// **'View your history to see all past meter readings and top-ups'**
  String get viewHistoryTip;

  /// Tip about notification organization
  ///
  /// In en, this message translates to:
  /// **'Notifications are grouped by type for easy organization'**
  String get notificationsGrouped;

  /// Tip about notification swipe actions
  ///
  /// In en, this message translates to:
  /// **'Swipe left on notifications to mark as read, right to delete'**
  String get swipeNotifications;

  /// Tip about configuring notification settings
  ///
  /// In en, this message translates to:
  /// **'Configure notification thresholds in Settings > Alerts & Notifications'**
  String get configureThresholds;

  /// Tip about low balance alerts
  ///
  /// In en, this message translates to:
  /// **'Low balance alerts help you avoid running out of credit'**
  String get lowBalanceHelp;

  /// Tip about days in advance setting
  ///
  /// In en, this message translates to:
  /// **'Set \\\"Days in Advance\\\" to get top-up reminders early'**
  String get daysInAdvanceTip;

  /// Today date label
  ///
  /// In en, this message translates to:
  /// **'Today'**
  String get today;

  /// Yesterday date label
  ///
  /// In en, this message translates to:
  /// **'Yesterday'**
  String get yesterday;

  /// Last week date label
  ///
  /// In en, this message translates to:
  /// **'Last week'**
  String get lastWeek;

  /// Last month date label
  ///
  /// In en, this message translates to:
  /// **'Last month'**
  String get lastMonth;

  /// Never date label
  ///
  /// In en, this message translates to:
  /// **'Never'**
  String get never;

  /// Plural form of day
  ///
  /// In en, this message translates to:
  /// **'days'**
  String get days;

  /// Singular form of day
  ///
  /// In en, this message translates to:
  /// **'day'**
  String get day;

  /// Plural form of hour
  ///
  /// In en, this message translates to:
  /// **'hours'**
  String get hours;

  /// Singular form of hour
  ///
  /// In en, this message translates to:
  /// **'hour'**
  String get hour;

  /// Plural form of minute
  ///
  /// In en, this message translates to:
  /// **'minutes'**
  String get minutes;

  /// Singular form of minute
  ///
  /// In en, this message translates to:
  /// **'minute'**
  String get minute;

  /// Retry button text
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// Skip button text
  ///
  /// In en, this message translates to:
  /// **'Skip'**
  String get skip;

  /// Complete status message
  ///
  /// In en, this message translates to:
  /// **'Complete'**
  String get complete;

  /// Failed status message
  ///
  /// In en, this message translates to:
  /// **'Failed'**
  String get failed;

  /// Syncing status message
  ///
  /// In en, this message translates to:
  /// **'Syncing...'**
  String get syncing;

  /// Deleting status message
  ///
  /// In en, this message translates to:
  /// **'Deleting...'**
  String get deleting;

  /// Message when no meter reading is available
  ///
  /// In en, this message translates to:
  /// **'No meter reading available'**
  String get noMeterReading;

  /// Instruction to add first meter reading
  ///
  /// In en, this message translates to:
  /// **'Add your first reading'**
  String get addFirstReading;

  /// Label for next top-up date
  ///
  /// In en, this message translates to:
  /// **'Next top-up'**
  String get nextTopUp;

  /// Button text to add meter reading
  ///
  /// In en, this message translates to:
  /// **'Add reading'**
  String get addReading;

  /// Button text to add top-up
  ///
  /// In en, this message translates to:
  /// **'Add top-up'**
  String get addTopUp;

  /// Message when no recent activity exists
  ///
  /// In en, this message translates to:
  /// **'No recent activity'**
  String get noRecentActivity;

  /// Validation error for invalid entry
  ///
  /// In en, this message translates to:
  /// **'Invalid entry'**
  String get invalidEntry;

  /// Validation error for missing data
  ///
  /// In en, this message translates to:
  /// **'Missing data'**
  String get missingData;

  /// Validation error for inconsistent data
  ///
  /// In en, this message translates to:
  /// **'Data inconsistency'**
  String get dataInconsistency;

  /// Generic validation error
  ///
  /// In en, this message translates to:
  /// **'Validation error'**
  String get validationError;

  /// Error message for save failure
  ///
  /// In en, this message translates to:
  /// **'Failed to save'**
  String get failedToSave;

  /// Error message for network issues
  ///
  /// In en, this message translates to:
  /// **'Network error'**
  String get networkError;

  /// Error message for permission issues
  ///
  /// In en, this message translates to:
  /// **'Permission denied'**
  String get permissionDenied;

  /// Error message for file not found
  ///
  /// In en, this message translates to:
  /// **'File not found'**
  String get fileNotFound;

  /// Error message for incorrect file format
  ///
  /// In en, this message translates to:
  /// **'Invalid file format'**
  String get invalidFileFormat;

  /// Dialog title for adding entry
  ///
  /// In en, this message translates to:
  /// **'Add Entry'**
  String get addEntryDialog;

  /// Dialog title for editing entry
  ///
  /// In en, this message translates to:
  /// **'Edit Entry'**
  String get editEntry;

  /// Dialog title for deleting entry
  ///
  /// In en, this message translates to:
  /// **'Delete Entry'**
  String get deleteEntry;

  /// Dialog title for delete confirmation
  ///
  /// In en, this message translates to:
  /// **'Confirm Delete'**
  String get confirmDelete;

  /// Dialog title for data export
  ///
  /// In en, this message translates to:
  /// **'Export Data'**
  String get exportData;

  /// Dialog title for data import
  ///
  /// In en, this message translates to:
  /// **'Import Data'**
  String get importData;

  /// Dialog title for settings
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settingsDialog;

  /// Dialog title for app information
  ///
  /// In en, this message translates to:
  /// **'About'**
  String get about;

  /// Notification message for low balance
  ///
  /// In en, this message translates to:
  /// **'Low balance alert'**
  String get lowBalanceAlert;

  /// Notification message for top-up reminder
  ///
  /// In en, this message translates to:
  /// **'Time to top up'**
  String get timeToTopUp;

  /// Notification message for meter reading reminder
  ///
  /// In en, this message translates to:
  /// **'Meter reading reminder'**
  String get meterReadingReminder;

  /// Notification message for data backup reminder
  ///
  /// In en, this message translates to:
  /// **'Data backup reminder'**
  String get dataBackupReminder;

  /// Settings category title for alerts and notifications
  ///
  /// In en, this message translates to:
  /// **'Alerts & Notifications'**
  String get alertsNotifications;

  /// Settings category title for date and time
  ///
  /// In en, this message translates to:
  /// **'Date & Time'**
  String get dateTime;

  /// Settings category title for theme
  ///
  /// In en, this message translates to:
  /// **'Theme'**
  String get theme;

  /// Settings category title for data management
  ///
  /// In en, this message translates to:
  /// **'Data Management'**
  String get dataManagement;

  /// Settings category title for app information
  ///
  /// In en, this message translates to:
  /// **'App Information'**
  String get appInformation;

  /// Setup screen title
  ///
  /// In en, this message translates to:
  /// **'Setup'**
  String get setup;

  /// Setup region settings section title
  ///
  /// In en, this message translates to:
  /// **'Region Settings'**
  String get setupRegionSettings;

  /// Setup region settings section description
  ///
  /// In en, this message translates to:
  /// **'Configure language and currency preferences.'**
  String get setupRegionSettingsDesc;

  /// Setup initial meter reading section title
  ///
  /// In en, this message translates to:
  /// **'Initial Meter Reading'**
  String get setupInitialMeterReading;

  /// Setup initial meter reading section description
  ///
  /// In en, this message translates to:
  /// **'Enter your current meter reading to start tracking.'**
  String get setupInitialMeterReadingDesc;

  /// Setup alert settings section title
  ///
  /// In en, this message translates to:
  /// **'Alert Settings'**
  String get setupAlertSettings;

  /// Setup alert settings section description
  ///
  /// In en, this message translates to:
  /// **'Configure when you want to receive alerts about your meter balance.'**
  String get setupAlertSettingsDesc;

  /// Setup date settings section title
  ///
  /// In en, this message translates to:
  /// **'Date Settings'**
  String get setupDateSettings;

  /// Setup date settings section description
  ///
  /// In en, this message translates to:
  /// **'Configure how dates are displayed in the app.'**
  String get setupDateSettingsDesc;

  /// Setup appearance section title
  ///
  /// In en, this message translates to:
  /// **'Appearance'**
  String get setupAppearance;

  /// Setup appearance section description
  ///
  /// In en, this message translates to:
  /// **'Customize the look and feel of the app.'**
  String get setupAppearanceDesc;

  /// Finish setup button text
  ///
  /// In en, this message translates to:
  /// **'Finish Setup'**
  String get finishSetup;

  /// Setup failed error message
  ///
  /// In en, this message translates to:
  /// **'Setup failed: {error}'**
  String setupFailed(String error);

  /// Validation error message for setup
  ///
  /// In en, this message translates to:
  /// **'Please check your inputs.'**
  String get pleaseCheckInputs;

  /// Date settings card title
  ///
  /// In en, this message translates to:
  /// **'Date Settings'**
  String get dateSettingsTitle;

  /// Date settings card description
  ///
  /// In en, this message translates to:
  /// **'Choose how dates will be displayed throughout the app.'**
  String get dateSettingsDesc;

  /// Date format subsection title
  ///
  /// In en, this message translates to:
  /// **'Date Format'**
  String get dateFormat;

  /// Alert threshold subsection title
  ///
  /// In en, this message translates to:
  /// **'Alert Threshold'**
  String get alertThreshold;

  /// Alert threshold description
  ///
  /// In en, this message translates to:
  /// **'You will be notified when your balance falls below this amount.'**
  String get alertThresholdDesc;

  /// Days in advance subsection title
  ///
  /// In en, this message translates to:
  /// **'Days in Advance'**
  String get daysInAdvance;

  /// Days in advance description
  ///
  /// In en, this message translates to:
  /// **'How many days before running out of credit to send reminders.'**
  String get daysInAdvanceDesc;

  /// Info notice for optional initial meter reading
  ///
  /// In en, this message translates to:
  /// **'This is optional. You can skip this step and add your first meter reading later.'**
  String get initialMeterReadingOptional;

  /// Error message when loading settings fails
  ///
  /// In en, this message translates to:
  /// **'Error loading settings: {error}'**
  String errorLoadingSettings(String error);
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['ar', 'de', 'en', 'es', 'fr', 'hi', 'pt', 'zh'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar': return AppLocalizationsAr();
    case 'de': return AppLocalizationsDe();
    case 'en': return AppLocalizationsEn();
    case 'es': return AppLocalizationsEs();
    case 'fr': return AppLocalizationsFr();
    case 'hi': return AppLocalizationsHi();
    case 'pt': return AppLocalizationsPt();
    case 'zh': return AppLocalizationsZh();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}
