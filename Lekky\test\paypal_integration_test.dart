import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:lekky/features/settings/providers/paypal_provider.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'dart:convert';

import 'paypal_integration_test.mocks.dart';

@GenerateMocks([http.Client, FlutterSecureStorage])
void main() {
  late PayPalService paypalService;
  late MockClient mockClient;
  late MockFlutterSecureStorage mockStorage;

  setUp(() {
    mockClient = MockClient();
    mockStorage = MockFlutterSecureStorage();
    paypalService = PayPalService(mockStorage, mockClient);
  });

  group('PayPal Donation Processing', () {
    test('should process donation successfully', () async {
      // Arrange
      const clientId = 'sandbox-client-id';
      const clientSecret = 'sandbox-secret';
      const amount = 50.0;
      const currency = 'USD';
      const description = 'Charity donation';
      const userId = 'user123';

      // Mock base64 encoded credentials
      when(mockStorage.read(key: 'paypalClientId'))
          .thenAnswer((_) async => base64Encode(utf8.encode(clientId)));
      when(mockStorage.read(key: 'paypalClientSecret'))
          .thenAnswer((_) async => base64Encode(utf8.encode(clientSecret)));

      // Mock successful order creation
      when(mockClient.post(
        Uri.parse('https://api.sandbox.paypal.com/v2/checkout/orders'),
        headers: anyNamed('headers'),
        body: anyNamed('body'),
      )).thenAnswer((_) async => http.Response('{"id":"ORDER-123"}', 201));

      // Mock successful database registration
      when(mockClient.post(
        Uri.parse('https://api.example.com/donations'),
        headers: anyNamed('headers'),
        body: anyNamed('body'),
      )).thenAnswer((_) async => http.Response('', 201));

      // Act
      final result = await paypalService.processDonation(
        amount: amount,
        currency: currency,
        description: description,
        userId: userId,
      );

      // Assert
      verify(mockStorage.read(key: 'paypalClientId')).called(1);
      verify(mockStorage.read(key: 'paypalClientSecret')).called(1);
      expect(result, 'ORDER-123');
    });

    test('should throw exception when credentials not initialized', () async {
      // Arrange
      when(mockStorage.read(key: 'paypalClientId')).thenAnswer((_) async => null);
      when(mockStorage.read(key: 'paypalClientSecret')).thenAnswer((_) async => null);

      // Act & Assert
      expect(
          () => paypalService.processDonation(
                amount: 50.0,
                currency: 'USD',
                description: 'Charity donation',
                userId: 'user123',
              ),
          throwsException);
    });

    test('should throw exception on failed order creation', () async {
      // Arrange
      when(mockStorage.read(key: 'paypalClientId'))
          .thenAnswer((_) async => base64Encode(utf8.encode('test')));
      when(mockStorage.read(key: 'paypalClientSecret'))
          .thenAnswer((_) async => base64Encode(utf8.encode('test')));
      when(mockClient.post(
        Uri.parse('https://api.sandbox.paypal.com/v2/checkout/orders'),
        headers: anyNamed('headers'),
        body: anyNamed('body'),
      )).thenAnswer(
          (_) async => http.Response('{"error":"invalid request"}', 400));

      // Act & Assert
      expect(
          () => paypalService.processDonation(
                amount: 50.0,
                currency: 'USD',
                description: 'Charity donation',
                userId: 'user123',
              ),
          throwsException);
    });

    test('should throw exception on failed database registration', () async {
      // Arrange
      const clientId = 'sandbox-client-id';
      const clientSecret = 'sandbox-secret';
      when(mockStorage.read(key: 'paypalClientId'))
          .thenAnswer((_) async => base64Encode(utf8.encode(clientId)));
      when(mockStorage.read(key: 'paypalClientSecret'))
          .thenAnswer((_) async => base64Encode(utf8.encode(clientSecret)));

      // First request (order creation) should succeed
      when(mockClient.post(
        Uri.parse('https://api.sandbox.paypal.com/v2/checkout/orders'),
        headers: anyNamed('headers'),
        body: anyNamed('body'),
      )).thenAnswer((_) async => http.Response('{"id":"ORDER-123"}', 201));

      // Second request (database registration) should fail
      when(mockClient.post(
        Uri.parse('https://api.example.com/donations'),
        headers: anyNamed('headers'),
        body: anyNamed('body'),
      )).thenAnswer(
          (_) async => http.Response('{"error":"server error"}', 500));

      // Act & Assert
      expect(
          () => paypalService.processDonation(
                amount: 50.0,
                currency: 'USD',
                description: 'Charity donation',
                userId: 'user123',
              ),
          throwsException);
    });
  });
}
