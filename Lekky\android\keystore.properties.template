# Keystore Configuration Template
# Copy this file to keystore.properties and fill in your actual values
# NEVER commit keystore.properties to version control

# Path to your keystore file (relative to android/ directory)
storeFile=your-keystore-file.jks

# Keystore password
storePassword=your-store-password

# Key alias name
keyAlias=your-key-alias

# Key password
keyPassword=your-key-password

# Security Notes:
# 1. Use strong, unique passwords for both store and key
# 2. Store keystore file securely and backup safely
# 3. Consider using environment variables for CI/CD
# 4. For production, use Android App Bundle signing by Google Play
