import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../core/providers/settings_provider.dart';
import '../../../../core/providers/settings_navigation_provider.dart';
import '../../../../core/widgets/lekky_button.dart';
import '../widgets/base_settings_screen.dart';

/// Donate screen
class DonateScreen extends BaseSettingsScreen {
  /// Constructor
  const DonateScreen({super.key})
      : super(
          title: 'Donation Options',
          categoryIndex: SettingsCategoryIndex.donate,
        );

  @override
  Widget buildContent(BuildContext context, WidgetRef ref) {
    return const _DonateScreenContent();
  }
}

/// Stateful content widget for donate screen
class _DonateScreenContent extends ConsumerStatefulWidget {
  const _DonateScreenContent();

  @override
  ConsumerState<_DonateScreenContent> createState() =>
      _DonateScreenContentState();
}

class _DonateScreenContentState extends ConsumerState<_DonateScreenContent> {
  @override
  Widget build(BuildContext context) {
    final settingsAsync = ref.watch(settingsProvider);

    return settingsAsync.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Text('Error loading settings: $error'),
      ),
      data: (settings) => ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          // Thank you card
          Card(
            margin: const EdgeInsets.only(bottom: 16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Row(
                    children: [
                      Icon(Icons.favorite, color: Colors.red),
                      SizedBox(width: 16),
                      Text(
                        'Support Lekky',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Thank you message
                  const Text(
                    'Thank you for considering a donation to support the development of Lekky. Your contribution helps us continue to improve the app and add new features.',
                    style: TextStyle(
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Payment methods card
          Card(
            margin: const EdgeInsets.only(bottom: 16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Row(
                    children: [
                      Icon(Icons.payment, color: Colors.blue),
                      SizedBox(width: 16),
                      Text(
                        'Payment Method',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // PayPal button
                  ElevatedButton.icon(
                    onPressed: () => _processPayPalDonation(context, ref),
                    icon: const Icon(Icons.paypal),
                    label: const Text('Donate with PayPal'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF0070BA),
                      foregroundColor: Colors.white,
                      minimumSize: const Size(double.infinity, 50),
                    ),
                  ),

                  const SizedBox(height: 8),

                  // Credit card button (placeholder)
                  ElevatedButton.icon(
                    onPressed: () {
                      _showComingSoonDialog(context);
                    },
                    icon: const Icon(Icons.credit_card),
                    label: const Text('Donate with Card'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey,
                      foregroundColor: Colors.white,
                      minimumSize: const Size(double.infinity, 50),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Note card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Row(
                    children: [
                      Icon(Icons.info, color: Colors.amber),
                      SizedBox(width: 16),
                      Text(
                        'Note',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Note text
                  const Text(
                    'All donations are voluntary and non-refundable. Lekky is a personal project and not a registered charity. Your donation will be used to support the development and maintenance of the app.',
                    style: TextStyle(
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Process PayPal donation using official PayPal donation button system
  ///
  /// SETUP INSTRUCTIONS:
  /// 1. Create a PayPal Business account (required for donations)
  /// 2. Go to https://www.paypal.com/donate/buttons
  /// 3. Set up your donation campaign (organization details, amounts, etc.)
  /// 4. PayPal will generate a button ID (e.g., ABC123XYZ)
  /// 5. Replace "YOUR_BUTTON_ID" below with your actual button ID
  /// 6. For testing, use sandbox: https://sandbox.paypal.com/donate/buttons
  Future<void> _processPayPalDonation(
      BuildContext context, WidgetRef ref) async {
    try {
      // PayPal donation buttons don't support custom amounts via URL parameters
      // Users will enter the amount directly on the PayPal page
      // Using the actual PayPal donation button ID: 7442VNKBWEYU8
      final paypalUrl =
          'https://www.paypal.com/donate/?hosted_button_id=7442VNKBWEYU8';

      final uri = Uri.parse(paypalUrl);

      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );

        // Show info dialog
        if (context.mounted) {
          _showPayPalInfoDialog(context);
        }
      } else {
        throw Exception('Could not launch PayPal donation page');
      }
    } catch (e) {
      // Show error dialog
      if (context.mounted) {
        _showErrorDialog(context, e.toString());
      }
    }
  }

  /// Show PayPal info dialog
  void _showPayPalInfoDialog(BuildContext context) {
    final theme = Theme.of(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final dialogWidth = _getDialogWidth(context);
    final horizontalPadding = (screenWidth - dialogWidth) / 2;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        elevation: 24,
        insetPadding: EdgeInsets.symmetric(
          horizontal: horizontalPadding,
          vertical: 28,
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildPayPalDialogHeader(context),
              const SizedBox(height: 16),
              Text(
                'You have been redirected to PayPal to complete your donation. Thank you for supporting Lekky App development!',
                style: TextStyle(
                  fontSize: 16,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 24),
              _buildDonationInstructions(context),
              const SizedBox(height: 16),
              Text(
                'Your donation helps keep the app free and supports continued development of new features. Every contribution, no matter the size, makes a difference!',
                style: TextStyle(
                  fontSize: 14,
                  color: theme.colorScheme.onSurface.withOpacity(0.8),
                ),
              ),
              const SizedBox(height: 24),
              _buildPayPalButtonBar(context),
            ],
          ),
        ),
      ),
    );
  }

  /// Calculate responsive dialog width
  double _getDialogWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    return screenWidth < 600 ? screenWidth * 0.95 : 500.0;
  }

  /// Build the PayPal dialog header
  Widget _buildPayPalDialogHeader(BuildContext context) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Icon(
          Icons.paypal,
          color: const Color(0xFF0070BA),
          size: 24,
        ),
        const SizedBox(width: 8),
        Text(
          'PayPal Donation',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const Spacer(),
        IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.of(context).pop(),
          tooltip: 'Close',
        ),
      ],
    );
  }

  /// Build the donation instructions
  Widget _buildDonationInstructions(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: theme.colorScheme.primary,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'How to Donate',
                  style: TextStyle(
                    fontSize: 14,
                    color: theme.colorScheme.onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Enter your desired donation amount on the PayPal page that just opened. You can choose any amount you\'re comfortable with.',
                  style: TextStyle(
                    fontSize: 13,
                    color: theme.colorScheme.onSurface.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build the PayPal button bar
  Widget _buildPayPalButtonBar(BuildContext context) {
    return Row(
      children: [
        const Spacer(),
        Expanded(
          child: LekkyButton(
            text: 'Got it',
            type: LekkyButtonType.primary,
            size: LekkyButtonSize.compact,
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
      ],
    );
  }

  /// Show error dialog
  void _showErrorDialog(BuildContext context, String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Donation Error'),
        content: Text('There was an error processing your donation: $error'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Show coming soon dialog for credit card
  void _showComingSoonDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Coming Soon'),
        content: const Text(
          'Credit card donations will be available in a future update. Please use PayPal for now.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
