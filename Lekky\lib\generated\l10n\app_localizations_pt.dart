import 'app_localizations.dart';

/// The translations for Portuguese (`pt`).
class AppLocalizationsPt extends AppLocalizations {
  AppLocalizationsPt([String locale = 'pt']) : super(locale);

  @override
  String get appName => 'Lekky';

  @override
  String get tagline => 'Seu Assistente de Medidor Pré-pago';

  @override
  String get splashQuote => 'Não sou barato—sou consciente dos quilowatts.';

  @override
  String get checkingPermissions => 'Verificando permissões...';

  @override
  String get initializing => 'Inicializando...';

  @override
  String get welcomeTitle => 'Bem-vindo ao Lekky';

  @override
  String get welcomeSubtitle => 'Seu assistente pessoal de medidor pré-pago';

  @override
  String get trackUsage => 'Acompanhe Seu Uso';

  @override
  String get getAlerts => 'Receba Alertas Oportunos';

  @override
  String get viewHistory => 'Ver Histórico';

  @override
  String get calculateCosts => 'Calcular Custos';

  @override
  String get trackUsageDesc => 'Monitore seu consumo e gastos de eletricidade';

  @override
  String get getAlertsDesc => 'Receba notificações quando seu saldo estiver baixo';

  @override
  String get viewHistoryDesc => 'Veja suas leituras de medidor e recargas anteriores';

  @override
  String get calculateCostsDesc => 'Estime seus custos de eletricidade em diferentes períodos';

  @override
  String get getStarted => 'Começar';

  @override
  String get restoreData => 'Restaurar Dados Anteriores';

  @override
  String get restoreHelper => 'Tem um backup de outro dispositivo?';

  @override
  String get restoreDataTitle => 'Restaurar Dados';

  @override
  String get restoreDataContent => 'Este recurso permitirá restaurar dados de um arquivo de backup.';

  @override
  String get cancel => 'Cancelar';

  @override
  String get chooseFile => 'Escolher Arquivo';

  @override
  String get regionSettings => 'Configurações Regionais';

  @override
  String get language => 'Idioma';

  @override
  String get currency => 'Moeda';

  @override
  String get selectLanguage => 'Selecione seu idioma preferido para a interface do aplicativo.';

  @override
  String get selectCurrency => 'Selecione a moeda para suas leituras de medidor.';

  @override
  String get currencyTip => 'Dica: Selecione a moeda que corresponde às suas contas de eletricidade.';

  @override
  String get perDay => '/dia';

  @override
  String get dashboard => 'Painel';

  @override
  String get history => 'Histórico';

  @override
  String get settings => 'Configurações';

  @override
  String get noEntriesFound => 'Nenhuma entrada encontrada';

  @override
  String get tryAdjustingFilters => 'Tente ajustar seus filtros para ver mais entradas';

  @override
  String get noEntriesYet => 'Ainda não há entradas';

  @override
  String get addFirstEntry => 'Adicione sua primeira leitura de medidor ou recarga para começar';

  @override
  String get errorLoadingData => 'Erro ao carregar dados';

  @override
  String errorLoadingPreferences(String error) {
    return 'Erro ao carregar preferências: $error';
  }

  @override
  String get meterReading => 'Leitura do Medidor';

  @override
  String get topUp => 'Recarga';

  @override
  String get lastUpdated => 'Última Atualização';

  @override
  String get daysRemaining => 'Dias Restantes';

  @override
  String get currentBalance => 'Saldo Atual';

  @override
  String get usageStatistics => 'Estatísticas de Uso';

  @override
  String get recentAverage => 'Média Recente';

  @override
  String get totalAverage => 'Média Total';

  @override
  String get dailyUsage => 'Uso Diário';

  @override
  String get topUpStatistics => 'Estatísticas de Recarga';

  @override
  String get daysToAlert => 'Dias para Alerta';

  @override
  String get daysToZero => 'Dias até Zero';

  @override
  String get quickActions => 'Ações Rápidas';

  @override
  String get addEntry => 'Adicionar Entrada';

  @override
  String get recentActivity => 'Atividade Recente';

  @override
  String get viewAll => 'Ver Tudo';

  @override
  String get save => 'Salvar';

  @override
  String get delete => 'Excluir';

  @override
  String get edit => 'Editar';

  @override
  String get add => 'Adicionar';

  @override
  String get close => 'Fechar';

  @override
  String get ok => 'OK';

  @override
  String get yes => 'Sim';

  @override
  String get no => 'Não';

  @override
  String get loading => 'Carregando...';

  @override
  String get saving => 'Salvando...';

  @override
  String get region => 'Região';

  @override
  String get languageCurrency => 'Idioma, Moeda';

  @override
  String get recentAvgUsage => 'A média recente mostra o uso entre leituras consecutivas';

  @override
  String get tapNotificationBell => 'Toque no ícone do sino de notificação para ver todas as notificações';

  @override
  String get addReadingsRegularly => 'Adicione novas leituras do medidor regularmente para melhores estatísticas de uso';

  @override
  String get setupAlertsLowBalance => 'Configure alertas para ser notificado quando o saldo estiver baixo';

  @override
  String get useQuickActions => 'Use as Ações Rápidas para adicionar novas leituras ou recargas';

  @override
  String get viewHistoryTip => 'Veja seu histórico para ver todas as leituras do medidor e recargas passadas';

  @override
  String get notificationsGrouped => 'As notificações são agrupadas por tipo para fácil organização';

  @override
  String get swipeNotifications => 'Deslize para a esquerda nas notificações para marcar como lidas, para a direita para excluir';

  @override
  String get configureThresholds => 'Configure os limites de notificação em Configurações > Alertas e Notificações';

  @override
  String get lowBalanceHelp => 'Os alertas de saldo baixo ajudam você a evitar ficar sem crédito';

  @override
  String get daysInAdvanceTip => 'Defina \\\"Dias de Antecedência\\\" para receber lembretes de recarga cedo';

  @override
  String get today => 'Hoje';

  @override
  String get yesterday => 'Ontem';

  @override
  String get lastWeek => 'Semana passada';

  @override
  String get lastMonth => 'Mês passado';

  @override
  String get never => 'Nunca';

  @override
  String get days => 'dias';

  @override
  String get day => 'dia';

  @override
  String get hours => 'horas';

  @override
  String get hour => 'hora';

  @override
  String get minutes => 'minutos';

  @override
  String get minute => 'minuto';

  @override
  String get retry => 'Tentar Novamente';

  @override
  String get skip => 'Pular';

  @override
  String get complete => 'Completo';

  @override
  String get failed => 'Falhou';

  @override
  String get syncing => 'Sincronizando...';

  @override
  String get deleting => 'Excluindo...';

  @override
  String get noMeterReading => 'Nenhuma leitura do medidor disponível';

  @override
  String get addFirstReading => 'Adicione sua primeira leitura';

  @override
  String get nextTopUp => 'Próxima recarga';

  @override
  String get addReading => 'Adicionar leitura';

  @override
  String get addTopUp => 'Adicionar recarga';

  @override
  String get noRecentActivity => 'Nenhuma atividade recente';

  @override
  String get invalidEntry => 'Entrada inválida';

  @override
  String get missingData => 'Dados ausentes';

  @override
  String get dataInconsistency => 'Inconsistência de dados';

  @override
  String get validationError => 'Erro de validação';

  @override
  String get failedToSave => 'Falha ao salvar';

  @override
  String get networkError => 'Erro de rede';

  @override
  String get permissionDenied => 'Permissão negada';

  @override
  String get fileNotFound => 'Arquivo não encontrado';

  @override
  String get invalidFileFormat => 'Formato de arquivo inválido';

  @override
  String get addEntryDialog => 'Adicionar entrada';

  @override
  String get editEntry => 'Editar entrada';

  @override
  String get deleteEntry => 'Excluir entrada';

  @override
  String get confirmDelete => 'Confirmar exclusão';

  @override
  String get exportData => 'Exportar dados';

  @override
  String get importData => 'Importar dados';

  @override
  String get settingsDialog => 'Configurações';

  @override
  String get about => 'Sobre';

  @override
  String get lowBalanceAlert => 'Alerta de saldo baixo';

  @override
  String get timeToTopUp => 'Hora de recarregar';

  @override
  String get meterReadingReminder => 'Lembrete de leitura do medidor';

  @override
  String get dataBackupReminder => 'Lembrete de backup de dados';

  @override
  String get alertsNotifications => 'Alertas e notificações';

  @override
  String get dateTime => 'Data e hora';

  @override
  String get theme => 'Tema';

  @override
  String get dataManagement => 'Gerenciamento de dados';

  @override
  String get appInformation => 'Informações do aplicativo';

  @override
  String get setup => 'Configuração';

  @override
  String get setupRegionSettings => 'Configurações Regionais';

  @override
  String get setupRegionSettingsDesc => 'Configure as preferências de idioma e moeda.';

  @override
  String get setupInitialMeterReading => 'Leitura Inicial do Medidor';

  @override
  String get setupInitialMeterReadingDesc => 'Digite sua leitura atual do medidor para começar o acompanhamento.';

  @override
  String get setupAlertSettings => 'Configurações de Alerta';

  @override
  String get setupAlertSettingsDesc => 'Configure quando você quer receber alertas sobre o saldo do seu medidor.';

  @override
  String get setupDateSettings => 'Configurações de Data';

  @override
  String get setupDateSettingsDesc => 'Configure como as datas são exibidas no aplicativo.';

  @override
  String get setupAppearance => 'Aparência';

  @override
  String get setupAppearanceDesc => 'Personalize a aparência do aplicativo.';

  @override
  String get finishSetup => 'Finalizar Configuração';

  @override
  String setupFailed(String error) {
    return 'Falha na configuração: $error';
  }

  @override
  String get pleaseCheckInputs => 'Por favor, verifique suas entradas.';

  @override
  String get dateSettingsTitle => 'Configurações de Data';

  @override
  String get dateSettingsDesc => 'Escolha como as datas serão exibidas em todo o aplicativo.';

  @override
  String get dateFormat => 'Formato de Data';

  @override
  String get alertThreshold => 'Limite de Alerta';

  @override
  String get alertThresholdDesc => 'Você será notificado quando seu saldo ficar abaixo deste valor.';

  @override
  String get daysInAdvance => 'Dias de Antecedência';

  @override
  String get daysInAdvanceDesc => 'Quantos dias antes de ficar sem crédito enviar lembretes.';

  @override
  String get initialMeterReadingOptional => 'Isso é opcional. Você pode pular esta etapa e adicionar sua primeira leitura do medidor mais tarde.';

  @override
  String errorLoadingSettings(String error) {
    return 'Erro ao carregar configurações: $error';
  }
}
