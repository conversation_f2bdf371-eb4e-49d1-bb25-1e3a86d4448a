// File: lib/core/navigation/navigation_extensions.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'app_routes.dart';
import 'navigation_service.dart';

/// Enhanced navigation extensions using the centralized navigation service
extension NavigationExtensions on BuildContext {
  /// Get navigation service from Riverpod
  NavigationService get _navigationService =>
      ProviderScope.containerOf(this).read(navigationServiceProvider);

  // Main Routes
  Future<T?> navigateToSplash<T>() =>
      _navigationService.navigateTo<T>(AppRoute.splash);
  Future<T?> navigateToWelcome<T>() =>
      _navigationService.navigateTo<T>(AppRoute.welcome);
  Future<T?> navigateToSetup<T>() =>
      _navigationService.navigateTo<T>(AppRoute.setup);
  Future<T?> navigateToHome<T>() =>
      _navigationService.navigateTo<T>(AppRoute.home);
  Future<T?> navigateToValidationDashboard<T>() =>
      _navigationService.navigateTo<T>(AppRoute.validationDashboard);

  // Main Tab Routes
  Future<T?> navigateToHistory<T>() =>
      _navigationService.navigateTo<T>(AppRoute.history);
  Future<T?> navigateToCost<T>() =>
      _navigationService.navigateTo<T>(AppRoute.cost);
  Future<T?> navigateToMainSettings<T>() =>
      _navigationService.navigateTo<T>(AppRoute.mainSettings);

  // Settings Routes
  Future<T?> navigateToSettings<T>({int? expanded}) =>
      _navigationService.navigateTo<T>(AppRoute.settings);

  // CSV Routes
  Future<T?> navigateToCsvExport<T>() =>
      _navigationService.navigateTo<T>(AppRoute.csvExport);
  Future<T?> navigateToCsvImport<T>() =>
      _navigationService.navigateTo<T>(AppRoute.csvImport);

  // Data Routes
  Future<T?> navigateToDeleteAllData<T>() =>
      _navigationService.navigateTo<T>(AppRoute.deleteAllData);

  // About Routes
  Future<T?> navigateToAbout<T>() =>
      _navigationService.navigateTo<T>(AppRoute.about);
  Future<T?> navigateToAboutInfo<T>() =>
      _navigationService.navigateTo<T>(AppRoute.aboutInfo);
  Future<T?> navigateToUpdate<T>() =>
      _navigationService.navigateTo<T>(AppRoute.update);
  Future<T?> navigateToTipsTricks<T>() =>
      _navigationService.navigateTo<T>(AppRoute.tipsTricks);

  // Donate Routes
  Future<T?> navigateToDonate<T>() =>
      _navigationService.navigateTo<T>(AppRoute.donate);
  Future<T?> navigateToDonateOptions<T>() =>
      _navigationService.navigateTo<T>(AppRoute.donateOptions);

  // Appearance Routes
  Future<T?> navigateToAppearance<T>() =>
      _navigationService.navigateTo<T>(AppRoute.appearance);
  Future<T?> navigateToTheme<T>() =>
      _navigationService.navigateTo<T>(AppRoute.theme);

  // Region Routes
  Future<T?> navigateToRegion<T>() =>
      _navigationService.navigateTo<T>(AppRoute.region);
  Future<T?> navigateToLanguage<T>() =>
      _navigationService.navigateTo<T>(AppRoute.language);
  Future<T?> navigateToCurrency<T>() =>
      _navigationService.navigateTo<T>(AppRoute.currency);

  // Date Routes
  Future<T?> navigateToDate<T>() =>
      _navigationService.navigateTo<T>(AppRoute.date);
  Future<T?> navigateToDateFormat<T>() =>
      _navigationService.navigateTo<T>(AppRoute.dateFormat);
  Future<T?> navigateToTimeDisplay<T>() =>
      _navigationService.navigateTo<T>(AppRoute.timeDisplay);

  // Notification Routes
  Future<T?> navigateToNotifications<T>() =>
      _navigationService.navigateTo<T>(AppRoute.notifications);
  Future<T?> navigateToAlertThreshold<T>() =>
      _navigationService.navigateTo<T>(AppRoute.alertThreshold);
  Future<T?> navigateToDaysAdvance<T>() =>
      _navigationService.navigateTo<T>(AppRoute.daysAdvance);
  Future<T?> navigateToNotificationTypes<T>() =>
      _navigationService.navigateTo<T>(AppRoute.notificationTypes);
  Future<T?> navigateToReminders<T>() =>
      _navigationService.navigateTo<T>(AppRoute.reminders);
  Future<T?> navigateToNotificationUtilities<T>() =>
      _navigationService.navigateTo<T>(AppRoute.notificationUtilities);

  // Debug Routes
  Future<T?> navigateToDebugNotifications<T>() =>
      _navigationService.navigateTo<T>(AppRoute.debugNotifications);

  // Declarative Navigation (go)
  void goToSplash() => _navigationService.goTo(AppRoute.splash);
  void goToWelcome() => _navigationService.goTo(AppRoute.welcome);
  void goToSetup() => _navigationService.goTo(AppRoute.setup);
  void goToHome() => _navigationService.goTo(AppRoute.home);
  void goToValidationDashboard() =>
      _navigationService.goTo(AppRoute.validationDashboard);

  void goToHistory() => _navigationService.goTo(AppRoute.history);
  void goToCost() => _navigationService.goTo(AppRoute.cost);
  void goToMainSettings() => _navigationService.goTo(AppRoute.mainSettings);

  void goToSettings({int? expanded}) =>
      _navigationService.goTo(AppRoute.settings);

  void goToCsvExport() => _navigationService.goTo(AppRoute.csvExport);
  void goToCsvImport() => _navigationService.goTo(AppRoute.csvImport);
  void goToDeleteAllData() => _navigationService.goTo(AppRoute.deleteAllData);

  void goToAbout() => _navigationService.goTo(AppRoute.about);
  void goToAboutInfo() => _navigationService.goTo(AppRoute.aboutInfo);
  void goToUpdate() => _navigationService.goTo(AppRoute.update);
  void goToTipsTricks() => _navigationService.goTo(AppRoute.tipsTricks);

  void goToDonate() => _navigationService.goTo(AppRoute.donate);
  void goToDonateOptions() => _navigationService.goTo(AppRoute.donateOptions);

  void goToAppearance() => _navigationService.goTo(AppRoute.appearance);
  void goToTheme() => _navigationService.goTo(AppRoute.theme);

  void goToRegion() => _navigationService.goTo(AppRoute.region);
  void goToLanguage() => _navigationService.goTo(AppRoute.language);
  void goToCurrency() => _navigationService.goTo(AppRoute.currency);

  void goToDate() => _navigationService.goTo(AppRoute.date);
  void goToDateFormat() => _navigationService.goTo(AppRoute.dateFormat);
  void goToTimeDisplay() => _navigationService.goTo(AppRoute.timeDisplay);

  void goToNotifications() => _navigationService.goTo(AppRoute.notifications);
  void goToAlertThreshold() => _navigationService.goTo(AppRoute.alertThreshold);
  void goToDaysAdvance() => _navigationService.goTo(AppRoute.daysAdvance);
  void goToNotificationTypes() =>
      _navigationService.goTo(AppRoute.notificationTypes);
  void goToReminders() => _navigationService.goTo(AppRoute.reminders);
  void goToNotificationUtilities() =>
      _navigationService.goTo(AppRoute.notificationUtilities);

  void goToDebugNotifications() =>
      _navigationService.goTo(AppRoute.debugNotifications);

  // Replacement Navigation
  Future<T?> replaceWithHome<T>() =>
      _navigationService.navigateToReplacement<T>(AppRoute.home);
  Future<T?> replaceWithHistory<T>() =>
      _navigationService.navigateToReplacement<T>(AppRoute.history);
  Future<T?> replaceWithSettings<T>({int? expanded}) =>
      _navigationService.navigateToReplacement<T>(AppRoute.settings);

  // Back Navigation
  void navigateBack() => _navigationService.goBack();
  void navigateBackWithResult<T>(T result) =>
      _navigationService.goBackWithResult<T>(result);
  bool canNavigateBack() => _navigationService.canGoBack();

  // Clear and Go Navigation
  void clearAndGoToHome() => _navigationService.clearAndGoTo(AppRoute.home);
  void clearAndGoToSettings({int? expanded}) =>
      _navigationService.clearAndGoTo(AppRoute.settings);

  // Current Location
  String get currentLocation => _navigationService.currentLocation;
}
