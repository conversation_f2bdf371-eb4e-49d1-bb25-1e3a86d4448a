import 'app_localizations.dart';

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get appName => 'Lekky';

  @override
  String get tagline => 'Votre Assistant de Compteur Prépayé';

  @override
  String get splashQuote => 'Je ne suis pas radin—je suis conscient des kilowatts.';

  @override
  String get checkingPermissions => 'Vérification des autorisations...';

  @override
  String get initializing => 'Initialisation...';

  @override
  String get welcomeTitle => 'Bienvenue sur Lekky';

  @override
  String get welcomeSubtitle => 'Votre assistant personnel de compteur prépayé';

  @override
  String get trackUsage => 'Suivez Votre Consommation';

  @override
  String get getAlerts => 'Recevez des Alertes Opportunes';

  @override
  String get viewHistory => 'Voir l\'Historique';

  @override
  String get calculateCosts => 'Calculer les Coûts';

  @override
  String get trackUsageDesc => 'Surveillez votre consommation et vos dépenses d\'électricité';

  @override
  String get getAlertsDesc => 'Recevez des notifications lorsque votre solde est faible';

  @override
  String get viewHistoryDesc => 'Consultez vos relevés de compteur et recharges précédents';

  @override
  String get calculateCostsDesc => 'Estimez vos coûts d\'électricité sur différentes périodes';

  @override
  String get getStarted => 'Commencer';

  @override
  String get restoreData => 'Restaurer les Données Précédentes';

  @override
  String get restoreHelper => 'Avez-vous une sauvegarde d\'un autre appareil ?';

  @override
  String get restoreDataTitle => 'Restaurer les Données';

  @override
  String get restoreDataContent => 'Cette fonctionnalité vous permettra de restaurer les données à partir d\'un fichier de sauvegarde.';

  @override
  String get cancel => 'Annuler';

  @override
  String get chooseFile => 'Choisir un Fichier';

  @override
  String get regionSettings => 'Paramètres Régionaux';

  @override
  String get language => 'Langue';

  @override
  String get currency => 'Devise';

  @override
  String get selectLanguage => 'Sélectionnez votre langue préférée pour l\'interface de l\'application.';

  @override
  String get selectCurrency => 'Sélectionnez la devise pour vos relevés de compteur.';

  @override
  String get currencyTip => 'Conseil : Sélectionnez la devise qui correspond à vos factures d\'électricité.';

  @override
  String get perDay => '/jour';

  @override
  String get dashboard => 'Tableau de Bord';

  @override
  String get history => 'Historique';

  @override
  String get settings => 'Paramètres';

  @override
  String get noEntriesFound => 'Aucune entrée trouvée';

  @override
  String get tryAdjustingFilters => 'Essayez d\'ajuster vos filtres pour voir plus d\'entrées';

  @override
  String get noEntriesYet => 'Aucune entrée pour le moment';

  @override
  String get addFirstEntry => 'Ajoutez votre premier relevé de compteur ou recharge pour commencer';

  @override
  String get errorLoadingData => 'Erreur lors du chargement des données';

  @override
  String errorLoadingPreferences(String error) {
    return 'Erreur lors du chargement des préférences : $error';
  }

  @override
  String get meterReading => 'Relevé de Compteur';

  @override
  String get topUp => 'Recharge';

  @override
  String get lastUpdated => 'Dernière Mise à Jour';

  @override
  String get daysRemaining => 'Jours Restants';

  @override
  String get currentBalance => 'Solde Actuel';

  @override
  String get usageStatistics => 'Statistiques d\'Utilisation';

  @override
  String get recentAverage => 'Moyenne Récente';

  @override
  String get totalAverage => 'Moyenne Totale';

  @override
  String get dailyUsage => 'Utilisation Quotidienne';

  @override
  String get topUpStatistics => 'Statistiques de Recharge';

  @override
  String get daysToAlert => 'Jours avant Alerte';

  @override
  String get daysToZero => 'Jours avant Zéro';

  @override
  String get quickActions => 'Actions Rapides';

  @override
  String get addEntry => 'Ajouter une Entrée';

  @override
  String get recentActivity => 'Activité Récente';

  @override
  String get viewAll => 'Voir Tout';

  @override
  String get save => 'Enregistrer';

  @override
  String get delete => 'Supprimer';

  @override
  String get edit => 'Modifier';

  @override
  String get add => 'Ajouter';

  @override
  String get close => 'Fermer';

  @override
  String get ok => 'OK';

  @override
  String get yes => 'Oui';

  @override
  String get no => 'Non';

  @override
  String get loading => 'Chargement...';

  @override
  String get saving => 'Enregistrement...';

  @override
  String get region => 'Région';

  @override
  String get languageCurrency => 'Langue, Devise';

  @override
  String get recentAvgUsage => 'La moyenne récente montre l\'utilisation entre deux lectures consécutives';

  @override
  String get tapNotificationBell => 'Appuyez sur l\'icône de cloche de notification pour voir toutes les notifications';

  @override
  String get addReadingsRegularly => 'Ajoutez régulièrement de nouveaux relevés de compteur pour de meilleures statistiques d\'utilisation';

  @override
  String get setupAlertsLowBalance => 'Configurez des alertes pour être averti lorsque votre solde est bas';

  @override
  String get useQuickActions => 'Utilisez les Actions Rapides pour ajouter de nouveaux relevés ou recharges';

  @override
  String get viewHistoryTip => 'Consultez votre historique pour voir tous vos relevés de compteur et recharges passés';

  @override
  String get notificationsGrouped => 'Les notifications sont regroupées par type pour une organisation facile';

  @override
  String get swipeNotifications => 'Glissez vers la gauche sur les notifications pour marquer comme lues, vers la droite pour supprimer';

  @override
  String get configureThresholds => 'Configurez les seuils de notification dans Paramètres > Alertes et Notifications';

  @override
  String get lowBalanceHelp => 'Les alertes de solde faible vous aident à éviter de manquer de crédit';

  @override
  String get daysInAdvanceTip => 'Définissez \\\"Jours à l\'Avance\\\" pour recevoir des rappels de recharge tôt';

  @override
  String get today => 'Aujourd\'hui';

  @override
  String get yesterday => 'Hier';

  @override
  String get lastWeek => 'La semaine dernière';

  @override
  String get lastMonth => 'Le mois dernier';

  @override
  String get never => 'Jamais';

  @override
  String get days => 'jours';

  @override
  String get day => 'jour';

  @override
  String get hours => 'heures';

  @override
  String get hour => 'heure';

  @override
  String get minutes => 'minutes';

  @override
  String get minute => 'minute';

  @override
  String get retry => 'Réessayer';

  @override
  String get skip => 'Ignorer';

  @override
  String get complete => 'Terminé';

  @override
  String get failed => 'Échec';

  @override
  String get syncing => 'Synchronisation...';

  @override
  String get deleting => 'Suppression...';

  @override
  String get noMeterReading => 'Aucune lecture de compteur disponible';

  @override
  String get addFirstReading => 'Ajoutez votre première lecture';

  @override
  String get nextTopUp => 'Prochaine recharge';

  @override
  String get addReading => 'Ajouter une lecture';

  @override
  String get addTopUp => 'Ajouter une recharge';

  @override
  String get noRecentActivity => 'Aucune activité récente';

  @override
  String get invalidEntry => 'Entrée invalide';

  @override
  String get missingData => 'Données manquantes';

  @override
  String get dataInconsistency => 'Incohérence des données';

  @override
  String get validationError => 'Erreur de validation';

  @override
  String get failedToSave => 'Échec de la sauvegarde';

  @override
  String get networkError => 'Erreur réseau';

  @override
  String get permissionDenied => 'Permission refusée';

  @override
  String get fileNotFound => 'Fichier introuvable';

  @override
  String get invalidFileFormat => 'Format de fichier invalide';

  @override
  String get addEntryDialog => 'Ajouter une entrée';

  @override
  String get editEntry => 'Modifier l\'entrée';

  @override
  String get deleteEntry => 'Supprimer l\'entrée';

  @override
  String get confirmDelete => 'Confirmer la suppression';

  @override
  String get exportData => 'Exporter les données';

  @override
  String get importData => 'Importer les données';

  @override
  String get settingsDialog => 'Paramètres';

  @override
  String get about => 'À propos';

  @override
  String get lowBalanceAlert => 'Alerte de solde faible';

  @override
  String get timeToTopUp => 'Il est temps de recharger';

  @override
  String get meterReadingReminder => 'Rappel de lecture du compteur';

  @override
  String get dataBackupReminder => 'Rappel de sauvegarde des données';

  @override
  String get alertsNotifications => 'Alertes et notifications';

  @override
  String get dateTime => 'Date et heure';

  @override
  String get theme => 'Thème';

  @override
  String get dataManagement => 'Gestion des données';

  @override
  String get appInformation => 'Informations sur l\'application';

  @override
  String get setup => 'Configuration';

  @override
  String get setupRegionSettings => 'Paramètres Régionaux';

  @override
  String get setupRegionSettingsDesc => 'Configurez les préférences de langue et de devise.';

  @override
  String get setupInitialMeterReading => 'Lecture Initiale du Compteur';

  @override
  String get setupInitialMeterReadingDesc => 'Entrez votre lecture actuelle du compteur pour commencer le suivi.';

  @override
  String get setupAlertSettings => 'Paramètres d\'Alerte';

  @override
  String get setupAlertSettingsDesc => 'Configurez quand vous voulez recevoir des alertes sur le solde de votre compteur.';

  @override
  String get setupDateSettings => 'Paramètres de Date';

  @override
  String get setupDateSettingsDesc => 'Configurez comment les dates sont affichées dans l\'application.';

  @override
  String get setupAppearance => 'Apparence';

  @override
  String get setupAppearanceDesc => 'Personnalisez l\'apparence de l\'application.';

  @override
  String get finishSetup => 'Terminer la Configuration';

  @override
  String setupFailed(String error) {
    return 'Échec de la configuration : $error';
  }

  @override
  String get pleaseCheckInputs => 'Veuillez vérifier vos saisies.';

  @override
  String get dateSettingsTitle => 'Paramètres de Date';

  @override
  String get dateSettingsDesc => 'Choisissez comment les dates seront affichées dans toute l\'application.';

  @override
  String get dateFormat => 'Format de Date';

  @override
  String get alertThreshold => 'Seuil d\'Alerte';

  @override
  String get alertThresholdDesc => 'Vous serez notifié lorsque votre solde tombe en dessous de ce montant.';

  @override
  String get daysInAdvance => 'Jours à l\'Avance';

  @override
  String get daysInAdvanceDesc => 'Combien de jours avant de manquer de crédit envoyer des rappels.';

  @override
  String get initialMeterReadingOptional => 'Ceci est optionnel. Vous pouvez ignorer cette étape et ajouter votre première lecture de compteur plus tard.';

  @override
  String errorLoadingSettings(String error) {
    return 'Erreur lors du chargement des paramètres : $error';
  }
}
