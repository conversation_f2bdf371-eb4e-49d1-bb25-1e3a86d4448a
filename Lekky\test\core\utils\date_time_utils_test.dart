import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/core/utils/date_time_utils.dart';

void main() {
  group('DateTimeUtils', () {
    group('calculateDaysWithPrecision', () {
      test('should calculate days between dates correctly', () {
        // Arrange
        final startDate = DateTime(2023, 1, 1);
        final endDate = DateTime(2023, 1, 11);

        // Act
        final days =
            DateTimeUtils.calculateDaysWithPrecision(startDate, endDate);

        // Assert
        expect(days, equals(10));
      });

      test('should handle same date correctly', () {
        // Arrange
        final date = DateTime(2023, 1, 1);

        // Act
        final days = DateTimeUtils.calculateDaysWithPrecision(date, date);

        // Assert
        expect(days, equals(0));
      });

      test('should handle reverse date order', () {
        // Arrange
        final startDate = DateTime(2023, 1, 11);
        final endDate = DateTime(2023, 1, 1);

        // Act
        final days =
            DateTimeUtils.calculateDaysWithPrecision(startDate, endDate);

        // Assert
        expect(days, equals(-10));
      });

      test('should handle dates across months', () {
        // Arrange
        final startDate = DateTime(2023, 1, 25);
        final endDate = DateTime(2023, 2, 5);

        // Act
        final days =
            DateTimeUtils.calculateDaysWithPrecision(startDate, endDate);

        // Assert
        expect(days, equals(11)); // 6 days in Jan + 5 days in Feb
      });

      test('should handle dates across years', () {
        // Arrange
        final startDate = DateTime(2022, 12, 25);
        final endDate = DateTime(2023, 1, 5);

        // Act
        final days =
            DateTimeUtils.calculateDaysWithPrecision(startDate, endDate);

        // Assert
        expect(days, equals(11)); // 7 days in Dec + 4 days in Jan
      });

      test('should handle leap year correctly', () {
        // Arrange
        final startDate = DateTime(2024, 2, 28); // 2024 is a leap year
        final endDate = DateTime(2024, 3, 1);

        // Act
        final days =
            DateTimeUtils.calculateDaysWithPrecision(startDate, endDate);

        // Assert
        expect(days, equals(2)); // Feb 29 + Mar 1
      });
    });

    group('startOfWeek', () {
      test('should return Monday for any day of the week', () {
        // Arrange - Wednesday, January 4, 2023
        final wednesday = DateTime(2023, 1, 4);

        // Act
        final startOfWeek = DateTimeUtils.startOfWeek(wednesday);

        // Assert
        expect(startOfWeek.weekday, equals(DateTime.monday));
        expect(startOfWeek.day, equals(2)); // Monday, January 2, 2023
      });

      test('should return same date if already Monday', () {
        // Arrange - Monday, January 2, 2023
        final monday = DateTime(2023, 1, 2);

        // Act
        final startOfWeek = DateTimeUtils.startOfWeek(monday);

        // Assert
        expect(startOfWeek, equals(monday));
      });

      test('should handle Sunday correctly', () {
        // Arrange - Sunday, January 8, 2023
        final sunday = DateTime(2023, 1, 8);

        // Act
        final startOfWeek = DateTimeUtils.startOfWeek(sunday);

        // Assert
        expect(startOfWeek.weekday, equals(DateTime.monday));
        expect(startOfWeek.day, equals(2)); // Monday, January 2, 2023
      });
    });

    group('endOfWeek', () {
      test('should return Sunday for any day of the week', () {
        // Arrange - Wednesday, January 4, 2023
        final wednesday = DateTime(2023, 1, 4);

        // Act
        final endOfWeek = DateTimeUtils.endOfWeek(wednesday);

        // Assert
        expect(endOfWeek.weekday, equals(DateTime.sunday));
        expect(endOfWeek.day, equals(8)); // Sunday, January 8, 2023
      });

      test('should return end of day for Sunday', () {
        // Arrange - Sunday, January 8, 2023
        final sunday = DateTime(2023, 1, 8);

        // Act
        final endOfWeek = DateTimeUtils.endOfWeek(sunday);

        // Assert
        expect(endOfWeek.weekday, equals(DateTime.sunday));
        expect(endOfWeek.hour, equals(23));
        expect(endOfWeek.minute, equals(59));
        expect(endOfWeek.second, equals(59));
      });
    });

    group('startOfDay', () {
      test('should return start of day', () {
        // Arrange
        final dateTime = DateTime(2023, 1, 15, 14, 30, 45);

        // Act
        final startOfDay = DateTimeUtils.startOfDay(dateTime);

        // Assert
        expect(startOfDay.year, equals(2023));
        expect(startOfDay.month, equals(1));
        expect(startOfDay.day, equals(15));
        expect(startOfDay.hour, equals(0));
        expect(startOfDay.minute, equals(0));
        expect(startOfDay.second, equals(0));
        expect(startOfDay.millisecond, equals(0));
      });
    });

    group('endOfDay', () {
      test('should return end of day', () {
        // Arrange
        final dateTime = DateTime(2023, 1, 15, 14, 30, 45);

        // Act
        final endOfDay = DateTimeUtils.endOfDay(dateTime);

        // Assert
        expect(endOfDay.year, equals(2023));
        expect(endOfDay.month, equals(1));
        expect(endOfDay.day, equals(15));
        expect(endOfDay.hour, equals(23));
        expect(endOfDay.minute, equals(59));
        expect(endOfDay.second, equals(59));
        expect(endOfDay.millisecond, equals(999));
      });
    });

    group('formatDate', () {
      test('should format date correctly with specified format', () {
        // Arrange
        final date = DateTime(2023, 1, 15);
        const format = 'yyyy-MM-dd';

        // Act
        final formatted = DateTimeUtils.formatDate(date, format);

        // Assert
        expect(formatted, isA<String>());
        expect(formatted, equals('2023-01-15'));
      });

      test('should handle different date formats', () {
        // Arrange
        final date = DateTime(2023, 1, 15);
        const format = 'dd/MM/yyyy';

        // Act
        final formatted = DateTimeUtils.formatDate(date, format);

        // Assert
        expect(formatted, equals('15/01/2023'));
      });
    });

    group('edge cases', () {
      test('should handle extreme dates', () {
        // Arrange
        final veryOldDate = DateTime(1900, 1, 1);
        final veryNewDate = DateTime(2100, 12, 31);

        // Act
        final days =
            DateTimeUtils.calculateDaysWithPrecision(veryOldDate, veryNewDate);

        // Assert
        expect(days, greaterThan(70000)); // Approximately 200 years
        expect(days.isFinite, isTrue);
      });

      test('should handle daylight saving time transitions', () {
        // Arrange - Dates around DST transition (if applicable)
        final beforeDST = DateTime(2023, 3, 11); // Before DST in US
        final afterDST = DateTime(2023, 3, 13); // After DST in US

        // Act
        final days =
            DateTimeUtils.calculateDaysWithPrecision(beforeDST, afterDST);

        // Assert
        expect(days, equals(2));
      });
    });
  });
}
