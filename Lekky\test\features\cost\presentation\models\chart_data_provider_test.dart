// File: test/features/cost/presentation/models/chart_data_provider_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/core/models/meter_entry.dart';
import 'package:lekky/features/cost/domain/models/cost_period.dart';
import 'package:lekky/features/cost/presentation/models/chart_data_provider.dart';

void main() {
  group('ChartDataProvider', () {
    test('generateChartData returns empty list for empty entries', () {
      final result = ChartDataProvider.generateChartData(
        entries: [],
        period: CostPeriod.pastDay,
        fromDate: DateTime(2023, 1, 1),
        toDate: DateTime(2023, 1, 1),
      );
      expect(result, isEmpty);
    });

    test('generateChartData generates hourly data for a day period', () {
      final entries = [
        MeterEntry(
          id: 1,
          date: DateTime(2023, 1, 1, 0, 0),
          reading: 100.0,
          amountToppedUp: 0.0,
          typeCode: 0,
        ),
        MeterEntry(
          id: 2,
          date: DateTime(2023, 1, 1, 6, 0),
          reading: 95.0,
          amountToppedUp: 0.0,
          typeCode: 0,
        ),
        MeterEntry(
          id: 3,
          date: DateTime(2023, 1, 1, 12, 0),
          reading: 90.0,
          amountToppedUp: 0.0,
          typeCode: 0,
        ),
        MeterEntry(
          id: 4,
          date: DateTime(2023, 1, 1, 18, 0),
          reading: 85.0,
          amountToppedUp: 0.0,
          typeCode: 0,
        ),
        MeterEntry(
          id: 5,
          date: DateTime(2023, 1, 1, 23, 59),
          reading: 80.0,
          amountToppedUp: 0.0,
          typeCode: 0,
        ),
      ];

      final result = ChartDataProvider.generateChartData(
        entries: entries,
        period: CostPeriod.pastDay,
        fromDate: DateTime(2023, 1, 1, 0, 0),
        toDate: DateTime(2023, 1, 1, 23, 59),
      );

      // Should generate 24 hourly data points
      expect(result.length, 24);

      // First data point should be for 00:00
      expect(result.first.date.hour, 0);

      // Last data point should be for 23:00
      expect(result.last.date.hour, 23);
    });

    test('generateChartData generates daily data for a week period', () {
      final entries = [
        MeterEntry(
          id: 1,
          date: DateTime(2023, 1, 1),
          reading: 100.0,
          amountToppedUp: 0.0,
          typeCode: 0,
        ),
        MeterEntry(
          id: 2,
          date: DateTime(2023, 1, 4),
          reading: 90.0,
          amountToppedUp: 0.0,
          typeCode: 0,
        ),
        MeterEntry(
          id: 3,
          date: DateTime(2023, 1, 7),
          reading: 80.0,
          amountToppedUp: 0.0,
          typeCode: 0,
        ),
      ];

      final result = ChartDataProvider.generateChartData(
        entries: entries,
        period: CostPeriod.pastWeek,
        fromDate: DateTime(2023, 1, 1),
        toDate: DateTime(2023, 1, 7),
      );

      // Should generate 7 daily data points
      expect(result.length, 7);

      // First data point should be for Jan 1
      expect(result.first.date.day, 1);

      // Last data point should be for Jan 7
      expect(result.last.date.day, 7);
    });

    test('generateChartData generates weekly data for a month period', () {
      final entries = [
        MeterEntry(
          id: 1,
          date: DateTime(2023, 1, 1),
          reading: 100.0,
          amountToppedUp: 0.0,
          typeCode: 0,
        ),
        MeterEntry(
          id: 2,
          date: DateTime(2023, 1, 15),
          reading: 80.0,
          amountToppedUp: 0.0,
          typeCode: 0,
        ),
        MeterEntry(
          id: 3,
          date: DateTime(2023, 1, 31),
          reading: 60.0,
          amountToppedUp: 0.0,
          typeCode: 0,
        ),
      ];

      final result = ChartDataProvider.generateChartData(
        entries: entries,
        period: CostPeriod.pastMonth,
        fromDate: DateTime(2023, 1, 1),
        toDate: DateTime(2023, 1, 31),
      );

      // Should generate weekly data points (6 for January 2023: partial weeks at start and end)
      expect(result.length, 6);

      // First data point should be for the week containing Jan 1 (starts Dec 26, 2022)
      expect(result.first.date.month, 12);
      expect(result.first.date.year, 2022);

      // Last data point should be for the week containing Jan 31 (starts Jan 30, 2023)
      expect(result.last.date.month, 1);
      expect(result.last.date.year, 2023);
    });

    test('generateChartData generates monthly data for a year period', () {
      final entries = [
        MeterEntry(
          id: 1,
          date: DateTime(2023, 1, 1),
          reading: 100.0,
          amountToppedUp: 0.0,
          typeCode: 0,
        ),
        MeterEntry(
          id: 2,
          date: DateTime(2023, 6, 15),
          reading: 80.0,
          amountToppedUp: 0.0,
          typeCode: 0,
        ),
        MeterEntry(
          id: 3,
          date: DateTime(2023, 12, 31),
          reading: 60.0,
          amountToppedUp: 0.0,
          typeCode: 0,
        ),
      ];

      final result = ChartDataProvider.generateChartData(
        entries: entries,
        period: CostPeriod.pastYear,
        fromDate: DateTime(2023, 1, 1),
        toDate: DateTime(2023, 12, 31),
      );

      // Should generate 12 monthly data points
      expect(result.length, 12);

      // First data point should be for Jan
      expect(result.first.date.month, 1);

      // Last data point should be for Dec
      expect(result.last.date.month, 12);
    });

    test('generateChartData handles custom periods correctly', () {
      final entries = [
        MeterEntry(
          id: 1,
          date: DateTime(2023, 1, 1),
          reading: 100.0,
          amountToppedUp: 0.0,
          typeCode: 0,
        ),
        MeterEntry(
          id: 2,
          date: DateTime(2023, 1, 5),
          reading: 90.0,
          amountToppedUp: 0.0,
          typeCode: 0,
        ),
        MeterEntry(
          id: 3,
          date: DateTime(2023, 1, 10),
          reading: 80.0,
          amountToppedUp: 0.0,
          typeCode: 0,
        ),
      ];

      final result = ChartDataProvider.generateChartData(
        entries: entries,
        period: CostPeriod.custom,
        fromDate: DateTime(2023, 1, 2),
        toDate: DateTime(2023, 1, 8),
      );

      // Should generate daily data for a 7-day custom period
      expect(result.length, 7);

      // First data point should be for Jan 2
      expect(result.first.date.day, 2);

      // Last data point should be for Jan 8
      expect(result.last.date.day, 8);
    });
  });
}
