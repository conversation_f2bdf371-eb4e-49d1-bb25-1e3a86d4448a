import 'package:flutter/material.dart';

import '../../features/validation/domain/models/validation_issue.dart';

/// Centralized validation color management with animation support
class ValidationColors {
  // Private constructor to prevent instantiation
  ValidationColors._();

  /// Animation duration for validation state transitions
  static const Duration animationDuration = Duration(milliseconds: 300);

  /// Animation curve for smooth validation transitions
  static const Curve animationCurve = Curves.easeInOut;

  // Severity Colors - Light Theme
  static const Color highSeverityLight = Colors.red;
  static const Color mediumSeverityLight = Colors.orange;
  static const Color lowSeverityLight = Colors.blue;
  static const Color noIssuesLight = Colors.white;

  // Severity Colors - Dark Theme
  static const Color highSeverityDark = Color(0xFFE57373); // Red 300
  static const Color mediumSeverityDark = Color(0xFFFFB74D); // Orange 300
  static const Color lowSeverityDark = Color(0xFF64B5F6); // Blue 300
  static const Color noIssuesDark = Colors.white;

  /// Get color for validation severity based on theme
  static Color getColorForSeverity(
    ValidationIssueSeverity? severity, {
    required bool isDarkTheme,
  }) {
    if (severity == null) {
      return isDarkTheme ? noIssuesDark : noIssuesLight;
    }

    switch (severity) {
      case ValidationIssueSeverity.high:
        return isDarkTheme ? highSeverityDark : highSeverityLight;
      case ValidationIssueSeverity.medium:
        return isDarkTheme ? mediumSeverityDark : mediumSeverityLight;
      case ValidationIssueSeverity.low:
        return isDarkTheme ? lowSeverityDark : lowSeverityLight;
    }
  }

  /// Get highest severity from a list of validation issues
  static ValidationIssueSeverity? getHighestSeverity(
      List<ValidationIssue> issues) {
    if (issues.isEmpty) return null;

    ValidationIssueSeverity? highest;
    for (final issue in issues) {
      if (highest == null || _compareSeverity(issue.severity, highest) > 0) {
        highest = issue.severity;
      }
    }
    return highest;
  }

  /// Compare two severities (returns positive if first is higher)
  static int _compareSeverity(
      ValidationIssueSeverity a, ValidationIssueSeverity b) {
    const severityOrder = {
      ValidationIssueSeverity.low: 1,
      ValidationIssueSeverity.medium: 2,
      ValidationIssueSeverity.high: 3,
    };
    return (severityOrder[a] ?? 0) - (severityOrder[b] ?? 0);
  }

  /// Get tooltip text for validation state
  static String getTooltipText(
      ValidationIssueSeverity? severity, int issueCount) {
    if (severity == null || issueCount == 0) {
      return 'Entry Validation - No issues found';
    }

    final severityText = _getSeverityDisplayText(severity);
    final issueText = issueCount == 1 ? 'issue' : 'issues';
    return 'Entry Validation - $issueCount $severityText $issueText detected';
  }

  /// Get display text for severity level
  static String _getSeverityDisplayText(ValidationIssueSeverity severity) {
    switch (severity) {
      case ValidationIssueSeverity.high:
        return 'high severity';
      case ValidationIssueSeverity.medium:
        return 'medium severity';
      case ValidationIssueSeverity.low:
        return 'low severity';
    }
  }

  /// Check if color transition animation should be used
  static bool shouldAnimateTransition(Color fromColor, Color toColor) {
    return fromColor != toColor;
  }

  /// Create color tween for smooth transitions
  static ColorTween createColorTween(Color begin, Color end) {
    return ColorTween(begin: begin, end: end);
  }
}
