// File: lib/features/cost/presentation/services/cost_calculation_service.dart

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/utils/logger.dart';
import '../../domain/models/cost_state.dart';
import '../../domain/models/cost_period.dart';
import '../../domain/models/cost_mode.dart';
import '../../domain/models/cost_result.dart';

import '../../data/cost_repository.dart';

/// Service for cost calculation business logic
class CostCalculationService {
  /// Calculate cost for the current state and period
  static Future<CostResult?> calculateCostForState(
    Ref ref,
    CostRepository costRepo,
    CostState currentState,
  ) async {
    try {
      final costResult = await costRepo.calculateCostForPeriod(
        currentState.selectedPeriod,
        currentState.fromDate,
        currentState.toDate,
      );

      Logger.info(
          'CostCalculationService: Cost calculated - ${costResult.costPerPeriod.toStringAsFixed(2)} for period ${currentState.selectedPeriod}');

      return costResult;
    } catch (error) {
      Logger.error('CostCalculationService: Failed to calculate cost: $error');
      return null;
    }
  }

  /// Calculate projected cost based on total average usage
  static Future<CostResult?> calculateProjectedCost(
    Ref ref,
    CostRepository costRepo,
    CostPeriod period,
    CostState currentState,
  ) async {
    try {
      // Get the total average usage
      final totalAverage = await costRepo.getTotalAverageUsage();
      if (totalAverage <= 0) {
        Logger.warning(
            'CostCalculationService: No valid total average usage available for projection');
        return null;
      }

      // Calculate projected cost based on period
      final projectedCost = await _calculateProjectedCostForPeriod(
        costRepo,
        period,
        totalAverage,
        currentState,
      );

      if (projectedCost != null) {
        Logger.info(
            'CostCalculationService: Projected cost calculated - ${projectedCost.costPerPeriod.toStringAsFixed(2)} for period $period');
      }

      return projectedCost;
    } catch (error) {
      Logger.error(
          'CostCalculationService: Failed to calculate projected cost: $error');
      return null;
    }
  }

  /// Calculate projected cost for specific period
  static Future<CostResult?> _calculateProjectedCostForPeriod(
    CostRepository costRepo,
    CostPeriod period,
    double totalAverage,
    CostState currentState,
  ) async {
    try {
      // Get meter unit
      final meterUnit = await costRepo.getMeterUnit();

      // Calculate days and cost based on period
      int days;
      DateTime fromDate;
      DateTime toDate;

      switch (period) {
        case CostPeriod.futureDay:
          days = 1;
          fromDate = DateTime.now();
          toDate = DateTime.now();
          break;
        case CostPeriod.futureWeek:
          days = 7;
          final now = DateTime.now();
          fromDate = now.subtract(Duration(days: now.weekday - 1));
          toDate = now.add(Duration(days: 7 - now.weekday));
          break;
        case CostPeriod.futureMonth:
          final now = DateTime.now();
          days = DateTime(now.year, now.month + 1, 0).day;
          fromDate = DateTime(now.year, now.month, 1);
          toDate = DateTime(now.year, now.month + 1, 0);
          break;
        case CostPeriod.futureYear:
          final now = DateTime.now();
          fromDate = DateTime(now.year, 1, 1);
          toDate = DateTime(now.year, 12, 31);
          days = toDate.difference(fromDate).inDays + 1;
          break;
        case CostPeriod.custom:
          if (currentState.fromDate != null && currentState.toDate != null) {
            fromDate = currentState.fromDate!;
            toDate = currentState.toDate!;
            days = toDate.difference(fromDate).inDays + 1;
          } else {
            return null;
          }
          break;
        default:
          Logger.warning(
              'CostCalculationService: Unsupported period for projection: $period');
          return null;
      }

      final costPerPeriod = totalAverage * days;

      return CostResult(
        averageUsage: totalAverage,
        costPerPeriod: costPerPeriod,
        period: period,
        meterUnit: meterUnit,
        actualDays: days,
        dailyRate: totalAverage,
        calculationMethod: CalculationMethod.totalAverageProjection,
        fromDate: fromDate,
        toDate: toDate,
      );
    } catch (error) {
      Logger.error(
          'CostCalculationService: Error calculating projected cost for period $period: $error');
      return null;
    }
  }

  /// Validate if cost calculation is possible for the given state
  static bool canCalculateCost(CostState state) {
    switch (state.selectedPeriod) {
      case CostPeriod.custom:
        return state.fromDate != null &&
            state.toDate != null &&
            state.dateRangeError == null;
      default:
        return true;
    }
  }

  /// Get cost calculation summary for logging
  static String getCostCalculationSummary(CostResult? result) {
    if (result == null) return 'No cost result available';

    return 'Cost: ${result.costPerPeriod.toStringAsFixed(2)}, '
        'Period: ${result.period}, '
        'From: ${result.fromDate?.toIso8601String().split('T')[0] ?? 'N/A'}, '
        'To: ${result.toDate?.toIso8601String().split('T')[0] ?? 'N/A'}, '
        'Days: ${result.actualDays}';
  }

  /// Determine if projection should be used based on cost mode
  static bool shouldUseProjection(CostMode costMode) {
    return costMode == CostMode.future;
  }

  /// Get appropriate calculation method based on cost mode and period
  static Future<CostResult?> calculateForMode(
    Ref ref,
    CostRepository costRepo,
    CostState currentState,
  ) async {
    // Custom date ranges always use historic calculation regardless of cost mode
    if (currentState.selectedPeriod == CostPeriod.custom) {
      Logger.info(
          'CostCalculationService: Using historic calculation for custom date range');
      return await calculateCostForState(ref, costRepo, currentState);
    }

    // For other periods, use cost mode to determine calculation type
    if (shouldUseProjection(currentState.costMode)) {
      Logger.info(
          'CostCalculationService: Using projection calculation for ${currentState.costMode} mode');
      return await calculateProjectedCost(
        ref,
        costRepo,
        currentState.selectedPeriod,
        currentState,
      );
    } else {
      Logger.info(
          'CostCalculationService: Using historic calculation for ${currentState.costMode} mode');
      return await calculateCostForState(ref, costRepo, currentState);
    }
  }
}
