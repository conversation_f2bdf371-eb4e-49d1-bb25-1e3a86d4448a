import '../../../../core/utils/date_time_utils.dart';
import '../../../../core/utils/logger.dart';
import '../../../meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../../top_ups/domain/repositories/top_up_repository.dart';
import '../models/cost_result.dart';

class HybridCostService {
  final MeterReadingRepository _meterReadingRepository;
  final TopUpRepository _topUpRepository;

  const HybridCostService(
    this._meterReadingRepository,
    this._topUpRepository,
  );

  /// Calculate custom period cost using interpolation method and return both cost and calculation method
  Future<Map<String, dynamic>> calculateCustomPeriodCostWithMethod(
      DateTime fromDate, DateTime toDate) async {
    final allReadings = await _meterReadingRepository.getAllMeterReadings();
    final allTopUps = await _topUpRepository.getAllTopUps();

    Logger.info(
        'HybridCostService: Calculating custom period cost using interpolation method from ${fromDate.toIso8601String()} to ${toDate.toIso8601String()}');

    if (allReadings.isEmpty) {
      Logger.warning('HybridCostService: No meter readings available');
      return {
        'cost': 0.0,
        'method': CalculationMethod.historicTotalAverage,
      };
    }

    // Use interpolation-based calculation for all custom periods
    final cost = await _calculateUsingInterpolation(
        fromDate, toDate, allReadings, allTopUps);

    Logger.info(
        'HybridCostService: Interpolation calculation completed with cost: $cost');

    return {
      'cost': cost,
      'method': CalculationMethod.historicRecentAverage,
    };
  }

  /// Legacy method for backward compatibility
  Future<double> calculateCustomPeriodCost(
      DateTime fromDate, DateTime toDate) async {
    final result = await calculateCustomPeriodCostWithMethod(fromDate, toDate);
    return result['cost'] as double;
  }

  /// Calculate cost using interpolation method (Grok's approach)
  Future<double> _calculateUsingInterpolation(DateTime fromDate,
      DateTime toDate, List allReadings, List allTopUps) async {
    if (allReadings.isEmpty) return 0.0;

    // Sort readings by date
    allReadings.sort((a, b) => a.date.compareTo(b.date));

    // Create intervals between consecutive meter readings
    final intervals = <Map<String, dynamic>>[];

    for (int i = 0; i < allReadings.length - 1; i++) {
      final currentReading = allReadings[i];
      final nextReading = allReadings[i + 1];

      // Calculate days between readings with precision
      final days = DateTimeUtils.calculateDaysWithPrecision(
          currentReading.date, nextReading.date);

      if (days <= 0) continue;

      // Sum any top-ups between the two readings
      double sumTopUps = 0.0;
      for (final topUp in allTopUps) {
        if (topUp.date.isAfter(currentReading.date) &&
            topUp.date.isBefore(nextReading.date)) {
          sumTopUps += topUp.amount;
        }
      }

      // Calculate usage: current_reading - next_reading + top_ups
      final usage = (currentReading.value - nextReading.value) + sumTopUps;
      if (usage > 0) {
        final dailyRate = usage / days;
        intervals.add({
          'startDate': currentReading.date,
          'endDate': nextReading.date,
          'dailyRate': dailyRate,
          'days': days,
        });
      }
    }

    // Calculate cost for the requested period by interpolating across intervals
    double totalCost = 0.0;

    for (final interval in intervals) {
      final intervalStart = interval['startDate'] as DateTime;
      final intervalEnd = interval['endDate'] as DateTime;
      final dailyRate = interval['dailyRate'] as double;

      // Find overlap between interval and requested period
      final overlapStart =
          intervalStart.isAfter(fromDate) ? intervalStart : fromDate;
      final overlapEnd = intervalEnd.isBefore(toDate) ? intervalEnd : toDate;

      if (overlapStart.isBefore(overlapEnd)) {
        final overlapDays =
            DateTimeUtils.calculateDaysWithPrecision(overlapStart, overlapEnd);
        totalCost += dailyRate * overlapDays;
      }
    }

    return totalCost;
  }
}
