// File: lib/features/backup/presentation/screens/import_results_screen.dart
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/models/meter_entry.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/utils/date_time_utils.dart';
import '../../../../core/widgets/app_card.dart';

/// A screen to display the results of an import operation
class ImportResultsScreen extends StatelessWidget {
  /// The imported entries
  final List<MeterEntry> entries;

  /// Any validation issues that were found
  final List<String> validationIssues;

  /// Whether the import replaced existing data
  final bool replacedExistingData;

  /// Callback when the user dismisses the screen
  final VoidCallback? onDismiss;

  /// Constructor
  const ImportResultsScreen({
    super.key,
    required this.entries,
    this.validationIssues = const [],
    this.replacedExistingData = false,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Import Results'),
        backgroundColor: AppColors.primary,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            // Navigate to main settings
            context.go('/main-settings');
          },
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSummaryCard(context),
            const SizedBox(height: 16),
            if (validationIssues.isNotEmpty) _buildIssuesCard(context),
            if (validationIssues.isNotEmpty) const SizedBox(height: 16),
            _buildEntriesCard(context),
            const SizedBox(height: 24),
            _buildDismissButton(context),
          ],
        ),
      ),
    );
  }

  /// Build a card with a summary of the import
  Widget _buildSummaryCard(BuildContext context) {
    // Count meter readings and top-ups
    final meterReadings = entries.where((e) => e.isReading).length;
    final topUps = entries.where((e) => e.isTopUp).length;

    // Get date range
    String dateRange = 'No entries';
    if (entries.isNotEmpty) {
      entries.sort((a, b) => a.timestamp.compareTo(b.timestamp));
      final firstDate =
          DateTimeUtils.formatDateWithMonthName(entries.first.timestamp);
      final lastDate =
          DateTimeUtils.formatDateWithMonthName(entries.last.timestamp);
      dateRange = '$firstDate to $lastDate';
    }

    return AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.green, size: 24),
              const SizedBox(width: 8),
              Text(
                'Import Successful',
                style: AppTextStyles.titleMedium.copyWith(
                  color: Colors.green[700],
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const Divider(),
          const SizedBox(height: 8),
          _buildInfoRow('Total Entries:', '${entries.length}'),
          const SizedBox(height: 4),
          _buildInfoRow('Meter Readings:', '$meterReadings'),
          const SizedBox(height: 4),
          _buildInfoRow('Top-ups:', '$topUps'),
          const SizedBox(height: 4),
          _buildInfoRow('Date Range:', dateRange),
          const SizedBox(height: 4),
          _buildInfoRow(
            'Import Mode:',
            replacedExistingData
                ? 'Replaced existing data'
                : 'Added to existing data',
          ),
        ],
      ),
    );
  }

  /// Build a card with validation issues
  Widget _buildIssuesCard(BuildContext context) {
    return AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.warning, color: Colors.orange, size: 24),
              const SizedBox(width: 8),
              Text(
                'Validation Issues',
                style: AppTextStyles.titleMedium.copyWith(
                  color: Colors.orange[700],
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const Divider(),
          const SizedBox(height: 8),
          Text(
            'The following issues were found but did not prevent the import:',
            style: AppTextStyles.bodyMedium,
          ),
          const SizedBox(height: 8),
          ...validationIssues.map((issue) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('• ',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    Expanded(child: Text(issue)),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  /// Build a card with a preview of the imported entries
  Widget _buildEntriesCard(BuildContext context) {
    // Sort entries by timestamp (newest first)
    final sortedEntries = List<MeterEntry>.from(entries)
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));

    // Take the first 10 entries for preview
    final previewEntries = sortedEntries.take(10).toList();

    return AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.list, color: AppColors.primary, size: 24),
              const SizedBox(width: 8),
              Text(
                'Recent Entries',
                style: AppTextStyles.titleMedium.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const Divider(),
          const SizedBox(height: 8),
          ...previewEntries.map((entry) => _buildEntryRow(context, entry)),
          if (entries.length > 10)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                '... and ${entries.length - 10} more entries',
                style: AppTextStyles.bodySmall.copyWith(
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
                textAlign: TextAlign.center,
              ),
            ),
        ],
      ),
    );
  }

  /// Build a row for a single entry
  Widget _buildEntryRow(BuildContext context, MeterEntry entry) {
    final formattedDate =
        DateTimeUtils.formatDateWithMonthName(entry.timestamp);
    final formattedTime = DateTimeUtils.formatTime(entry.timestamp);

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: entry.isTopUp ? Colors.green : AppColors.primary,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            flex: 3,
            child: Text(
              '$formattedDate $formattedTime',
              style: AppTextStyles.bodySmall,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              entry.typeDisplayName,
              style: AppTextStyles.bodySmall.copyWith(
                color: entry.isTopUp ? Colors.green : AppColors.primary,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              entry.isTopUp
                  ? entry.amountToppedUp.toStringAsFixed(2)
                  : entry.reading.toStringAsFixed(2),
              style: AppTextStyles.bodySmall.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  /// Build a button to dismiss the screen
  Widget _buildDismissButton(BuildContext context) {
    return Center(
      child: ElevatedButton(
        onPressed: () {
          // Navigate to main settings
          context.go('/main-settings');
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
        ),
        child: const Text('Done'),
      ),
    );
  }

  /// Build an information row with a label and value
  Widget _buildInfoRow(String label, String value) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Text(
            label,
            style: AppTextStyles.bodyMedium.copyWith(
              color: Colors.grey[700],
            ),
          ),
        ),
        Expanded(
          flex: 3,
          child: Text(
            value,
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }
}
