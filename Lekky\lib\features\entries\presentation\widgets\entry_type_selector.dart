import 'package:flutter/material.dart';
import '../controllers/entry_controller.dart';

/// A segmented control for selecting the entry type
class EntryTypeSelector extends StatelessWidget {
  /// The currently selected entry type
  final EntryType selectedType;

  /// Callback when the entry type changes
  final ValueChanged<EntryType> onTypeChanged;

  /// Whether the selector is enabled
  final bool enabled;

  /// Whether this is for editing a records gap entry (both buttons start grey)
  final bool isDismissalMode;

  /// Constructor
  const EntryTypeSelector({
    super.key,
    required this.selectedType,
    required this.onTypeChanged,
    this.enabled = true,
    this.isDismissalMode = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // If this is a records gap entry, show a special read-only display
    if (selectedType == EntryType.recordsGap) {
      return Container(
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: theme.colorScheme.outline.withOpacity(0.5),
            width: 1,
          ),
        ),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          decoration: BoxDecoration(
            color: Colors.grey.withOpacity(0.3),
            borderRadius: BorderRadius.circular(7),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.change_history,
                size: 18,
                color: Colors.grey[600],
              ),
              const SizedBox(width: 8),
              Text(
                'Records Entry Gap',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Normal two-button selector for meter reading and top-up
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.5),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          _buildSegment(
            context,
            EntryType.meterReading,
            'Meter Reading',
            Icons.speed,
          ),
          Container(
            width: 1,
            height: 40,
            color: theme.colorScheme.outline.withOpacity(0.5),
          ),
          _buildSegment(
            context,
            EntryType.topUp,
            'Top-up',
            Icons.add_card,
          ),
        ],
      ),
    );
  }

  /// Build a single segment of the control
  Widget _buildSegment(
    BuildContext context,
    EntryType type,
    String label,
    IconData icon,
  ) {
    final theme = Theme.of(context);
    final isSelected = selectedType == type;

    // Handle dismissal mode - both buttons start grey until selected
    Color backgroundColor;
    Color textColor;

    if (isDismissalMode && !isSelected) {
      // Very light grey background for unselected dismissal mode
      backgroundColor = Colors.grey.shade100;
      textColor = Colors.grey.shade600; // Darker grey text
    } else if (isSelected) {
      // Use different colors based on entry type when selected
      backgroundColor = type == EntryType.topUp
          ? theme.colorScheme.secondary // Orange for top-up
          : theme.colorScheme.primary; // Blue for meter reading
      textColor = type == EntryType.topUp
          ? theme.colorScheme.onSecondary // Text color on orange
          : theme.colorScheme.onPrimary; // Text color on blue
    } else {
      // Normal unselected state
      backgroundColor = theme.colorScheme.surface;
      textColor = theme.colorScheme.onSurface;
    }

    return Expanded(
      child: InkWell(
        onTap: enabled ? () => onTypeChanged(type) : null,
        borderRadius: BorderRadius.horizontal(
          left: type == EntryType.meterReading
              ? const Radius.circular(8)
              : Radius.zero,
          right:
              type == EntryType.topUp ? const Radius.circular(8) : Radius.zero,
        ),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: isSelected
                ? backgroundColor
                : (isDismissalMode ? backgroundColor : Colors.transparent),
            borderRadius: BorderRadius.horizontal(
              left: type == EntryType.meterReading
                  ? const Radius.circular(7)
                  : Radius.zero,
              right: type == EntryType.topUp
                  ? const Radius.circular(7)
                  : Radius.zero,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 18,
                color: textColor,
              ),
              const SizedBox(width: 8),
              Text(
                label,
                style: TextStyle(
                  color: textColor,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
