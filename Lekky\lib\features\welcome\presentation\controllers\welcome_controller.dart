import 'package:flutter/material.dart';

/// Controller for the welcome screen
class WelcomeController extends ChangeNotifier {
  /// Whether the controller is loading
  final bool _isLoading = false;

  /// Error message if any
  String? _error;

  /// Get whether the controller is loading
  bool get isLoading => _isLoading;

  /// Get the error message if any
  String? get error => _error;

  /// Mark the welcome screen as completed (no-op since setup completion is the gate)
  Future<void> markWelcomeCompleted() async {
    // Welcome completion is no longer tracked separately
    // Setup completion is the authoritative gate for onboarding
    return;
  }
}
