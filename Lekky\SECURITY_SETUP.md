# Security Setup Guide

## PayPal Integration Security

This document outlines the security measures implemented for PayPal donation processing in the Lekky application.

### Key Security Features

1. **Secure Credential Storage**
   - PayPal client ID and secret are stored using `flutter_secure_storage` with platform-specific encryption
   - Credentials are base64 encoded before storage for additional obfuscation
   - Storage is configured with platform-specific security options:
     - Android: Encrypted SharedPreferences
     - iOS: Keychain with `unlocked` accessibility

2. **State Management**
   - Implemented with Riverpod for secure state isolation
   - Service dependencies are properly scoped and injected
   - No sensitive data is exposed in UI layers

3. **Payment Processing**
   - Direct communication with PayPal's REST API using secured HTTPS
   - Credentials are decoded only during payment processing
   - No sensitive data is stored in local database after processing

### Configuration Steps

1. **Obtain PayPal Credentials**
   - Create a PayPal developer account
   - Generate sandbox/live credentials in the Dashboard
   - Never commit credentials to version control

2. **Initialize PayPal Service**
```dart
// In your settings screen
final paypal = ref.read(paypalServiceProvider);

try {
  await paypal.initializePayPal(
    clientId: _clientIdController.text,
    clientSecret: _clientSecretController.text,
  );
  // Show success message
} catch (e) {
  // Handle error
}
```

3. **Process Donations**
```dart
final orderId = await paypal.processDonation(
  amount: 50.0,
  currency: 'USD',
  description: 'Charity donation',
  userId: currentUser.id,
);
```

### Security Verification

1. Verify credentials are properly stored:
```bash
# Check secure storage (platform-specific)
# Android: adb shell "run-as com.example.lekky cat shared_prefs/flutter_secure_storage.xml"
# iOS: Use Xcode debug tools to inspect Keychain
```

2. Test error handling scenarios:
   - Missing credentials
   - Invalid API responses
   - Network failures
   - Timeout conditions

### Compliance Considerations

- Ensure compliance with PCI DSS requirements
- Do not store full payment details locally
- Use PayPal's server-side verification for production
- Regularly rotate API credentials
- Implement fraud detection mechanisms

### Testing Verification

All PayPal integration features include comprehensive unit tests covering:
- Successful payment flow
- Error handling scenarios
- Security boundary conditions
- Credential management

Run tests with:
```bash
flutter test test/paypal_integration_test.dart
