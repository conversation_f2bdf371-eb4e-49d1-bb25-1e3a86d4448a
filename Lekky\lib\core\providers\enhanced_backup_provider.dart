import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../features/backup/backup_service.dart';
import '../database/database_helper.dart';
import '../constants/database_constants.dart';
import '../models/meter_entry.dart';

import '../utils/logger.dart';

/// Provider for the enhanced backup service
final enhancedBackupServiceProvider = Provider<BackupService>((ref) {
  return BackupService();
});

/// Provider for backup operations with state management
final backupStateProvider =
    StateNotifierProvider<BackupStateNotifier, BackupState>((ref) {
  final backupService = ref.watch(enhancedBackupServiceProvider);
  return BackupStateNotifier(backupService);
});

/// Backup state for tracking operations
class BackupState {
  final bool isExporting;
  final bool isImporting;
  final double progress;
  final String? statusMessage;
  final String? filePath;
  final String? errorMessage;

  const BackupState({
    this.isExporting = false,
    this.isImporting = false,
    this.progress = 0.0,
    this.statusMessage,
    this.filePath,
    this.errorMessage,
  });

  BackupState copyWith({
    bool? isExporting,
    bool? isImporting,
    double? progress,
    String? statusMessage,
    String? filePath,
    String? errorMessage,
  }) {
    return BackupState(
      isExporting: isExporting ?? this.isExporting,
      isImporting: isImporting ?? this.isImporting,
      progress: progress ?? this.progress,
      statusMessage: statusMessage ?? this.statusMessage,
      filePath: filePath ?? this.filePath,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

/// State notifier for managing backup operations
class BackupStateNotifier extends StateNotifier<BackupState> {
  final BackupService _backupService;

  BackupStateNotifier(this._backupService) : super(const BackupState());

  /// Export data to CSV with progress tracking
  Future<void> exportToCsv() async {
    print('🔄 BackupStateNotifier: Starting exportToCsv()');

    state = state.copyWith(
      isExporting: true,
      progress: 0.0,
      statusMessage: 'Starting export...',
      errorMessage: null,
      filePath: null,
    );
    print('🔄 BackupStateNotifier: State updated - isExporting: true');

    try {
      // Update progress
      state = state.copyWith(
        progress: 0.2,
        statusMessage: 'Retrieving data from database...',
      );
      print(
          '🔄 BackupStateNotifier: Progress 0.2 - Retrieving data from database');

      // Get all entries from database
      final entries = await _getAllEntriesForExport();
      print(
          '🔄 BackupStateNotifier: Retrieved ${entries.length} entries from database');

      state = state.copyWith(
        progress: 0.6,
        statusMessage: 'Creating CSV file...',
      );
      print('🔄 BackupStateNotifier: Progress 0.6 - Creating CSV file');

      // Export using backup service
      print(
          '🔄 BackupStateNotifier: Calling _backupService.exportMeterEntries()');
      final result = await _backupService.exportMeterEntries(entries: entries);
      print(
          '🔄 BackupStateNotifier: Export result received - isSuccess: ${result.isSuccess}');

      if (result.isSuccess) {
        print(
            '🔄 BackupStateNotifier: Export successful - path: ${result.value.path}');
        state = state.copyWith(
          isExporting: false,
          progress: 1.0,
          statusMessage: 'Export completed successfully',
          filePath: result.value.path,
        );
        print(
            '🔄 BackupStateNotifier: Final state updated - isExporting: false, filePath: ${result.value.path}');
      } else {
        print(
            '🔄 BackupStateNotifier: Export failed - error: ${result.error.message}');
        state = state.copyWith(
          isExporting: false,
          progress: 0.0,
          statusMessage: null,
          errorMessage: result.error.message,
        );
        print(
            '🔄 BackupStateNotifier: Error state updated - errorMessage: ${result.error.message}');
      }
    } catch (e) {
      print('🔄 BackupStateNotifier: Exception caught: $e');
      state = state.copyWith(
        isExporting: false,
        progress: 0.0,
        statusMessage: null,
        errorMessage: 'Export failed: $e',
      );
      print(
          '🔄 BackupStateNotifier: Exception state updated - errorMessage: Export failed: $e');
    }

    print('🔄 BackupStateNotifier: exportToCsv() completed');
  }

  /// Import data from CSV file
  Future<void> importFromFile(String filePath) async {
    state = state.copyWith(
      isImporting: true,
      progress: 0.0,
      statusMessage: 'Starting import...',
      errorMessage: null,
    );

    try {
      state = state.copyWith(
        progress: 0.3,
        statusMessage: 'Reading CSV file...',
      );

      // Import using backup service
      final result = await _backupService.importMeterEntries(File(filePath));

      if (result.isSuccess) {
        state = state.copyWith(
          isImporting: false,
          progress: 1.0,
          statusMessage: 'Import completed successfully',
        );
      } else {
        state = state.copyWith(
          isImporting: false,
          progress: 0.0,
          statusMessage: null,
          errorMessage: result.error.message,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isImporting: false,
        progress: 0.0,
        statusMessage: null,
        errorMessage: 'Import failed: $e',
      );
    }
  }

  /// Get all entries for export
  Future<List<MeterEntry>> _getAllEntriesForExport() async {
    try {
      final databaseHelper = DatabaseHelper();
      final db = await databaseHelper.database;

      // Get all meter readings and top-ups
      final meterReadings =
          await db.query(DatabaseConstants.meterReadingsTable);
      final topUps = await db.query(DatabaseConstants.topUpsTable);

      final entries = <MeterEntry>[];

      // Add meter readings with proper Type=2 handling
      for (final reading in meterReadings) {
        final status = reading['status'] as int? ??
            ((reading['is_valid'] as int?) == 1 ? 0 : 1);
        final typeCode = status == 2 ? 2 : 0;

        entries.add(MeterEntry.fromTypeCodeAndAmount(
          id: reading['id'] as int?,
          typeCode: typeCode,
          amount: reading['value'] as double,
          timestamp: DateTime.parse(reading['date'] as String),
          notes: reading['notes'] as String?,
        ));
      }

      // Add top-ups
      for (final topUp in topUps) {
        entries.add(MeterEntry.fromTypeCodeAndAmount(
          id: topUp['id'] as int?,
          typeCode: 1,
          amount: topUp['amount'] as double,
          timestamp: DateTime.parse(topUp['date'] as String),
          notes: topUp['notes'] as String?,
        ));
      }

      // Sort entries by date
      entries.sort((a, b) => a.date.compareTo(b.date));

      Logger.info(
          'Enhanced backup: Retrieved ${entries.length} entries for export');
      return entries;
    } catch (e) {
      Logger.error('Enhanced backup: Error getting entries: $e');
      return [];
    }
  }

  /// Reset backup state
  void reset() {
    state = const BackupState();
  }
}
