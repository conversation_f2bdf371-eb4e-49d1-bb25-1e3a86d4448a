{"@@locale": "en", "@@last_modified": "2025-01-17T00:00:00.000Z", "appName": "Le<PERSON><PERSON>", "@appName": {"description": "The name of the application"}, "tagline": "Your Prepaid Meter Assistant", "@tagline": {"description": "App tagline displayed on splash screen"}, "splashQuote": "I'm not cheap—I'm kilowatt-conscious.", "@splashQuote": {"description": "Humorous quote displayed on splash screen"}, "checkingPermissions": "Checking permissions...", "@checkingPermissions": {"description": "Status message while checking app permissions"}, "initializing": "Initializing...", "@initializing": {"description": "Status message during app initialization"}, "welcomeTitle": "Welcome to Lekky", "@welcomeTitle": {"description": "Main title on welcome screen"}, "welcomeSubtitle": "Your personal prepaid meter assistant", "@welcomeSubtitle": {"description": "Subtitle on welcome screen"}, "trackUsage": "Track Your Usage", "@trackUsage": {"description": "Feature title for usage tracking"}, "getAlerts": "Get <PERSON><PERSON>s", "@getAlerts": {"description": "Feature title for alert notifications"}, "viewHistory": "View History", "@viewHistory": {"description": "Feature title for viewing history"}, "calculateCosts": "Calculate Costs", "@calculateCosts": {"description": "Feature title for cost calculation"}, "trackUsageDesc": "Monitor your electricity consumption and spending", "@trackUsageDesc": {"description": "Description for usage tracking feature"}, "getAlertsDesc": "Receive notifications when your balance is running low", "@getAlertsDesc": {"description": "Description for alert notifications feature"}, "viewHistoryDesc": "See your past meter readings and top-ups", "@viewHistoryDesc": {"description": "Description for history viewing feature"}, "calculateCostsDesc": "Estimate your electricity costs over different periods", "@calculateCostsDesc": {"description": "Description for cost calculation feature"}, "getStarted": "Get Started", "@getStarted": {"description": "Button text to begin app setup"}, "restoreData": "Restore Previous Data", "@restoreData": {"description": "Button text for data restoration option"}, "restoreHelper": "Have a backup from another device?", "@restoreHelper": {"description": "Helper text for data restoration"}, "restoreDataTitle": "Restore Data", "@restoreDataTitle": {"description": "Dialog title for data restoration"}, "restoreDataContent": "This feature will allow you to restore data from a backup file.", "@restoreDataContent": {"description": "Dialog content explaining data restoration"}, "cancel": "Cancel", "@cancel": {"description": "Generic cancel button text"}, "chooseFile": "Choose <PERSON>", "@chooseFile": {"description": "Button text for file selection"}, "regionSettings": "Region Settings", "@regionSettings": {"description": "Title for regional settings section"}, "language": "Language", "@language": {"description": "Label for language setting"}, "currency": "<PERSON><PERSON><PERSON><PERSON>", "@currency": {"description": "Label for currency setting"}, "selectLanguage": "Select your preferred language for the app interface.", "@selectLanguage": {"description": "Instruction for language selection"}, "selectCurrency": "Select the currency for your meter readings.", "@selectCurrency": {"description": "Instruction for currency selection"}, "currencyTip": "Tip: Select the currency that matches your electricity bills.", "@currencyTip": {"description": "Helpful tip for currency selection"}, "perDay": "/day", "@perDay": {"description": "Suffix for daily rate display"}, "dashboard": "Dashboard", "@dashboard": {"description": "Dashboard screen title"}, "history": "History", "@history": {"description": "History screen title"}, "settings": "Settings", "@settings": {"description": "Settings screen title"}, "noEntriesFound": "No entries found", "@noEntriesFound": {"description": "Message when no history entries match filters"}, "tryAdjustingFilters": "Try adjusting your filters to see more entries", "@tryAdjustingFilters": {"description": "Suggestion when no entries found with filters"}, "noEntriesYet": "No entries yet", "@noEntriesYet": {"description": "Message when no history entries exist"}, "addFirstEntry": "Add your first meter reading or top-up to get started", "@addFirstEntry": {"description": "Instruction for first-time users"}, "errorLoadingData": "Error loading data", "@errorLoadingData": {"description": "Generic error message for data loading failures"}, "errorLoadingPreferences": "Error loading preferences: {error}", "@errorLoadingPreferences": {"description": "Error message when preferences fail to load", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "meterReading": "Meter Reading", "@meterReading": {"description": "Label for meter reading entries"}, "topUp": "Top Up", "@topUp": {"description": "Label for top-up entries"}, "lastUpdated": "Last Updated", "@lastUpdated": {"description": "Label for last update timestamp"}, "daysRemaining": "Days Remaining", "@daysRemaining": {"description": "Label for estimated days until meter runs out"}, "currentBalance": "Current Balance", "@currentBalance": {"description": "Label for current meter balance"}, "usageStatistics": "Usage Statistics", "@usageStatistics": {"description": "Title for usage statistics card"}, "recentAverage": "Recent Average", "@recentAverage": {"description": "Label for recent average usage"}, "totalAverage": "Total Average", "@totalAverage": {"description": "Label for total average usage"}, "dailyUsage": "Daily Usage", "@dailyUsage": {"description": "Label for daily usage amount"}, "topUpStatistics": "Top Up Statistics", "@topUpStatistics": {"description": "Title for top-up statistics card"}, "daysToAlert": "Days to Alert", "@daysToAlert": {"description": "Label for days until alert threshold"}, "daysToZero": "Days to Zero", "@daysToZero": {"description": "Label for days until meter reaches zero"}, "quickActions": "Quick Actions", "@quickActions": {"description": "Title for quick actions card"}, "addEntry": "Add Entry", "@addEntry": {"description": "Button text to add new entry"}, "recentActivity": "Recent Activity", "@recentActivity": {"description": "Title for recent activity card"}, "viewAll": "View All", "@viewAll": {"description": "Button text to view all items"}, "save": "Save", "@save": {"description": "Generic save button text"}, "delete": "Delete", "@delete": {"description": "Generic delete button text"}, "edit": "Edit", "@edit": {"description": "Generic edit button text"}, "add": "Add", "@add": {"description": "Generic add button text"}, "close": "Close", "@close": {"description": "Generic close button text"}, "ok": "OK", "@ok": {"description": "Generic OK button text"}, "yes": "Yes", "@yes": {"description": "Generic yes button text"}, "no": "No", "@no": {"description": "Generic no button text"}, "loading": "Loading...", "@loading": {"description": "Generic loading message"}, "saving": "Saving...", "@saving": {"description": "Generic saving message"}, "region": "Region", "@region": {"description": "Settings category for regional settings"}, "languageCurrency": "Language, Currency", "@languageCurrency": {"description": "Description for region settings category"}, "recentAvgUsage": "Recent-avg shows usage between consecutive readings", "@recentAvgUsage": {"description": "Description of recent average usage calculation"}, "tapNotificationBell": "Tap the notification bell icon to view all notifications", "@tapNotificationBell": {"description": "Tip about notification bell functionality"}, "addReadingsRegularly": "Add new meter readings regularly for better usage statistics", "@addReadingsRegularly": {"description": "Tip about adding regular readings"}, "setupAlertsLowBalance": "Set up alerts to be notified when your balance is low", "@setupAlertsLowBalance": {"description": "Tip about setting up low balance alerts"}, "useQuickActions": "Use the Quick Actions to add new readings or top-ups", "@useQuickActions": {"description": "Tip about using quick actions"}, "viewHistoryTip": "View your history to see all past meter readings and top-ups", "@viewHistoryTip": {"description": "Tip about viewing history"}, "notificationsGrouped": "Notifications are grouped by type for easy organization", "@notificationsGrouped": {"description": "Tip about notification organization"}, "swipeNotifications": "Swipe left on notifications to mark as read, right to delete", "@swipeNotifications": {"description": "Tip about notification swipe actions"}, "configureThresholds": "Configure notification thresholds in Settings > Alerts & Notifications", "@configureThresholds": {"description": "Tip about configuring notification settings"}, "lowBalanceHelp": "Low balance alerts help you avoid running out of credit", "@lowBalanceHelp": {"description": "Tip about low balance alerts"}, "daysInAdvanceTip": "Set \\\"Days in Advance\\\" to get top-up reminders early", "@daysInAdvanceTip": {"description": "Tip about days in advance setting"}, "today": "Today", "@today": {"description": "Today date label"}, "yesterday": "Yesterday", "@yesterday": {"description": "Yesterday date label"}, "lastWeek": "Last week", "@lastWeek": {"description": "Last week date label"}, "lastMonth": "Last month", "@lastMonth": {"description": "Last month date label"}, "never": "Never", "@never": {"description": "Never date label"}, "days": "days", "@days": {"description": "Plural form of day"}, "day": "day", "@day": {"description": "Singular form of day"}, "hours": "hours", "@hours": {"description": "Plural form of hour"}, "hour": "hour", "@hour": {"description": "Singular form of hour"}, "minutes": "minutes", "@minutes": {"description": "Plural form of minute"}, "minute": "minute", "@minute": {"description": "Singular form of minute"}, "retry": "Retry", "@retry": {"description": "Retry button text"}, "skip": "<PERSON><PERSON>", "@skip": {"description": "Skip button text"}, "complete": "Complete", "@complete": {"description": "Complete status message"}, "failed": "Failed", "@failed": {"description": "Failed status message"}, "syncing": "Syncing...", "@syncing": {"description": "Syncing status message"}, "deleting": "Deleting...", "@deleting": {"description": "Deleting status message"}, "noMeterReading": "No meter reading available", "@noMeterReading": {"description": "Message when no meter reading is available"}, "addFirstReading": "Add your first reading", "@addFirstReading": {"description": "Instruction to add first meter reading"}, "nextTopUp": "Next top-up", "@nextTopUp": {"description": "Label for next top-up date"}, "addReading": "Add reading", "@addReading": {"description": "Button text to add meter reading"}, "addTopUp": "Add top-up", "@addTopUp": {"description": "Button text to add top-up"}, "noRecentActivity": "No recent activity", "@noRecentActivity": {"description": "Message when no recent activity exists"}, "invalidEntry": "Invalid entry", "@invalidEntry": {"description": "Validation error for invalid entry"}, "missingData": "Missing data", "@missingData": {"description": "Validation error for missing data"}, "dataInconsistency": "Data inconsistency", "@dataInconsistency": {"description": "Validation error for inconsistent data"}, "validationError": "Validation error", "@validationError": {"description": "Generic validation error"}, "failedToSave": "Failed to save", "@failedToSave": {"description": "Error message for save failure"}, "networkError": "Network error", "@networkError": {"description": "Error message for network issues"}, "permissionDenied": "Permission denied", "@permissionDenied": {"description": "Error message for permission issues"}, "fileNotFound": "File not found", "@fileNotFound": {"description": "Error message for file not found"}, "invalidFileFormat": "Invalid file format", "@invalidFileFormat": {"description": "Error message for incorrect file format"}, "addEntryDialog": "Add Entry", "@addEntryDialog": {"description": "Dialog title for adding entry"}, "editEntry": "Edit Entry", "@editEntry": {"description": "Dialog title for editing entry"}, "deleteEntry": "Delete Entry", "@deleteEntry": {"description": "Dialog title for deleting entry"}, "confirmDelete": "Confirm Delete", "@confirmDelete": {"description": "Dialog title for delete confirmation"}, "exportData": "Export Data", "@exportData": {"description": "Dialog title for data export"}, "importData": "Import Data", "@importData": {"description": "Dialog title for data import"}, "settingsDialog": "Settings", "@settingsDialog": {"description": "Dialog title for settings"}, "about": "About", "@about": {"description": "Dialog title for app information"}, "lowBalanceAlert": "Low balance alert", "@lowBalanceAlert": {"description": "Notification message for low balance"}, "timeToTopUp": "Time to top up", "@timeToTopUp": {"description": "Notification message for top-up reminder"}, "meterReadingReminder": "Meter reading reminder", "@meterReadingReminder": {"description": "Notification message for meter reading reminder"}, "dataBackupReminder": "Data backup reminder", "@dataBackupReminder": {"description": "Notification message for data backup reminder"}, "alertsNotifications": "Alerts & Notifications", "@alertsNotifications": {"description": "Settings category title for alerts and notifications"}, "dateTime": "Date & Time", "@dateTime": {"description": "Settings category title for date and time"}, "theme": "Theme", "@theme": {"description": "Settings category title for theme"}, "dataManagement": "Data Management", "@dataManagement": {"description": "Settings category title for data management"}, "appInformation": "App Information", "@appInformation": {"description": "Settings category title for app information"}, "setup": "Setup", "@setup": {"description": "Setup screen title"}, "setupRegionSettings": "Region Settings", "@setupRegionSettings": {"description": "Setup region settings section title"}, "setupRegionSettingsDesc": "Configure language and currency preferences.", "@setupRegionSettingsDesc": {"description": "Setup region settings section description"}, "setupInitialMeterReading": "Initial Meter Reading", "@setupInitialMeterReading": {"description": "Setup initial meter reading section title"}, "setupInitialMeterReadingDesc": "Enter your current meter reading to start tracking.", "@setupInitialMeterReadingDesc": {"description": "Setup initial meter reading section description"}, "setupAlertSettings": "<PERSON><PERSON>", "@setupAlertSettings": {"description": "Setup alert settings section title"}, "setupAlertSettingsDesc": "Configure when you want to receive alerts about your meter balance.", "@setupAlertSettingsDesc": {"description": "Setup alert settings section description"}, "setupDateSettings": "Date Settings", "@setupDateSettings": {"description": "Setup date settings section title"}, "setupDateSettingsDesc": "Configure how dates are displayed in the app.", "@setupDateSettingsDesc": {"description": "Setup date settings section description"}, "setupAppearance": "Appearance", "@setupAppearance": {"description": "Setup appearance section title"}, "setupAppearanceDesc": "Customize the look and feel of the app.", "@setupAppearanceDesc": {"description": "Setup appearance section description"}, "finishSetup": "Finish Setup", "@finishSetup": {"description": "Finish setup button text"}, "setupFailed": "Setup failed: {error}", "@setupFailed": {"description": "Setup failed error message", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "pleaseCheckInputs": "Please check your inputs.", "@pleaseCheckInputs": {"description": "Validation error message for setup"}, "dateSettingsTitle": "Date Settings", "@dateSettingsTitle": {"description": "Date settings card title"}, "dateSettingsDesc": "Choose how dates will be displayed throughout the app.", "@dateSettingsDesc": {"description": "Date settings card description"}, "dateFormat": "Date Format", "@dateFormat": {"description": "Date format subsection title"}, "alertThreshold": "<PERSON><PERSON>", "@alertThreshold": {"description": "Alert threshold subsection title"}, "alertThresholdDesc": "You will be notified when your balance falls below this amount.", "@alertThresholdDesc": {"description": "Alert threshold description"}, "daysInAdvance": "Days in Advance", "@daysInAdvance": {"description": "Days in advance subsection title"}, "daysInAdvanceDesc": "How many days before running out of credit to send reminders.", "@daysInAdvanceDesc": {"description": "Days in advance description"}, "initialMeterReadingOptional": "This is optional. You can skip this step and add your first meter reading later.", "@initialMeterReadingOptional": {"description": "Info notice for optional initial meter reading"}, "errorLoadingSettings": "Error loading settings: {error}", "@errorLoadingSettings": {"description": "Error message when loading settings fails", "placeholders": {"error": {"type": "String", "description": "Error details"}}}}