import 'app_localizations.dart';

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get appName => 'Lekky';

  @override
  String get tagline => '您的预付费电表助手';

  @override
  String get splashQuote => '我不是小气——我是千瓦时意识强。';

  @override
  String get checkingPermissions => '正在检查权限...';

  @override
  String get initializing => '正在初始化...';

  @override
  String get welcomeTitle => '欢迎使用 Lekky';

  @override
  String get welcomeSubtitle => '您的个人预付费电表助手';

  @override
  String get trackUsage => '跟踪您的用量';

  @override
  String get getAlerts => '获取及时提醒';

  @override
  String get viewHistory => '查看历史记录';

  @override
  String get calculateCosts => '计算费用';

  @override
  String get trackUsageDesc => '监控您的电力消耗和支出';

  @override
  String get getAlertsDesc => '当您的余额不足时接收通知';

  @override
  String get viewHistoryDesc => '查看您过去的电表读数和充值记录';

  @override
  String get calculateCostsDesc => '估算您在不同时期的电费';

  @override
  String get getStarted => '开始使用';

  @override
  String get restoreData => '恢复以前的数据';

  @override
  String get restoreHelper => '您有来自其他设备的备份吗？';

  @override
  String get restoreDataTitle => '恢复数据';

  @override
  String get restoreDataContent => '此功能将允许您从备份文件恢复数据。';

  @override
  String get cancel => '取消';

  @override
  String get chooseFile => '选择文件';

  @override
  String get regionSettings => '区域设置';

  @override
  String get language => '语言';

  @override
  String get currency => '货币';

  @override
  String get selectLanguage => '为应用界面选择您的首选语言。';

  @override
  String get selectCurrency => '为您的电表读数选择货币。';

  @override
  String get currencyTip => '提示：选择与您的电费账单匹配的货币。';

  @override
  String get perDay => '/天';

  @override
  String get dashboard => '仪表板';

  @override
  String get history => '历史记录';

  @override
  String get settings => '设置';

  @override
  String get noEntriesFound => '未找到条目';

  @override
  String get tryAdjustingFilters => '尝试调整您的筛选器以查看更多条目';

  @override
  String get noEntriesYet => '暂无条目';

  @override
  String get addFirstEntry => '添加您的第一个电表读数或充值以开始使用';

  @override
  String get errorLoadingData => '加载数据时出错';

  @override
  String errorLoadingPreferences(String error) {
    return '加载首选项时出错：$error';
  }

  @override
  String get meterReading => '电表读数';

  @override
  String get topUp => '充值';

  @override
  String get lastUpdated => '最后更新';

  @override
  String get daysRemaining => '剩余天数';

  @override
  String get currentBalance => '当前余额';

  @override
  String get usageStatistics => '使用统计';

  @override
  String get recentAverage => '近期平均';

  @override
  String get totalAverage => '总平均';

  @override
  String get dailyUsage => '日用量';

  @override
  String get topUpStatistics => '充值统计';

  @override
  String get daysToAlert => '距离提醒天数';

  @override
  String get daysToZero => '距离归零天数';

  @override
  String get quickActions => '快速操作';

  @override
  String get addEntry => '添加条目';

  @override
  String get recentActivity => '最近活动';

  @override
  String get viewAll => '查看全部';

  @override
  String get save => '保存';

  @override
  String get delete => '删除';

  @override
  String get edit => '编辑';

  @override
  String get add => '添加';

  @override
  String get close => '关闭';

  @override
  String get ok => '确定';

  @override
  String get yes => '是';

  @override
  String get no => '否';

  @override
  String get loading => '加载中...';

  @override
  String get saving => '保存中...';

  @override
  String get region => '区域';

  @override
  String get languageCurrency => '语言，货币';

  @override
  String get recentAvgUsage => '近期平均值显示连续读数之间的使用情况';

  @override
  String get tapNotificationBell => '点击通知铃铛图标查看所有通知';

  @override
  String get addReadingsRegularly => '定期添加新的电表读数以获得更好的使用统计';

  @override
  String get setupAlertsLowBalance => '设置提醒，以便在余额不足时收到通知';

  @override
  String get useQuickActions => '使用快速操作添加新的读数或充值';

  @override
  String get viewHistoryTip => '查看您的历史记录以查看所有过去的电表读数和充值';

  @override
  String get notificationsGrouped => '通知按类型分组，便于组织';

  @override
  String get swipeNotifications => '向左滑动通知标记为已读，向右滑动删除';

  @override
  String get configureThresholds => '在设置 > 提醒和通知中配置通知阈值';

  @override
  String get lowBalanceHelp => '低余额提醒帮助您避免余额不足';

  @override
  String get daysInAdvanceTip => '设置\"提前天数\"以提前收到充值提醒';

  @override
  String get today => '今天';

  @override
  String get yesterday => '昨天';

  @override
  String get lastWeek => '上周';

  @override
  String get lastMonth => '上个月';

  @override
  String get never => '从不';

  @override
  String get days => '天';

  @override
  String get day => '天';

  @override
  String get hours => '小时';

  @override
  String get hour => '小时';

  @override
  String get minutes => '分钟';

  @override
  String get minute => '分钟';

  @override
  String get retry => '重试';

  @override
  String get skip => '跳过';

  @override
  String get complete => '完成';

  @override
  String get failed => '失败';

  @override
  String get syncing => '同步中...';

  @override
  String get deleting => '删除中...';

  @override
  String get noMeterReading => '没有可用的电表读数';

  @override
  String get addFirstReading => '添加您的第一个读数';

  @override
  String get nextTopUp => '下次充值';

  @override
  String get addReading => '添加读数';

  @override
  String get addTopUp => '添加充值';

  @override
  String get noRecentActivity => '没有最近活动';

  @override
  String get invalidEntry => '无效条目';

  @override
  String get missingData => '数据缺失';

  @override
  String get dataInconsistency => '数据不一致';

  @override
  String get validationError => '验证错误';

  @override
  String get failedToSave => '保存失败';

  @override
  String get networkError => '网络错误';

  @override
  String get permissionDenied => '权限被拒绝';

  @override
  String get fileNotFound => '文件未找到';

  @override
  String get invalidFileFormat => '无效文件格式';

  @override
  String get addEntryDialog => '添加条目';

  @override
  String get editEntry => '编辑条目';

  @override
  String get deleteEntry => '删除条目';

  @override
  String get confirmDelete => '确认删除';

  @override
  String get exportData => '导出数据';

  @override
  String get importData => '导入数据';

  @override
  String get settingsDialog => '设置';

  @override
  String get about => '关于';

  @override
  String get lowBalanceAlert => '余额不足提醒';

  @override
  String get timeToTopUp => '该充值了';

  @override
  String get meterReadingReminder => '电表读数提醒';

  @override
  String get dataBackupReminder => '数据备份提醒';

  @override
  String get alertsNotifications => '提醒和通知';

  @override
  String get dateTime => '日期和时间';

  @override
  String get theme => '主题';

  @override
  String get dataManagement => '数据管理';

  @override
  String get appInformation => '应用信息';

  @override
  String get setup => '设置';

  @override
  String get setupRegionSettings => '地区设置';

  @override
  String get setupRegionSettingsDesc => '配置语言和货币偏好。';

  @override
  String get setupInitialMeterReading => '初始电表读数';

  @override
  String get setupInitialMeterReadingDesc => '输入您当前的电表读数以开始跟踪。';

  @override
  String get setupAlertSettings => '提醒设置';

  @override
  String get setupAlertSettingsDesc => '配置何时接收关于电表余额的提醒。';

  @override
  String get setupDateSettings => '日期设置';

  @override
  String get setupDateSettingsDesc => '配置应用中日期的显示方式。';

  @override
  String get setupAppearance => '外观';

  @override
  String get setupAppearanceDesc => '自定义应用的外观。';

  @override
  String get finishSetup => '完成设置';

  @override
  String setupFailed(String error) {
    return '设置失败：$error';
  }

  @override
  String get pleaseCheckInputs => '请检查您的输入。';

  @override
  String get dateSettingsTitle => '日期设置';

  @override
  String get dateSettingsDesc => '选择整个应用中日期的显示方式。';

  @override
  String get dateFormat => '日期格式';

  @override
  String get alertThreshold => '提醒阈值';

  @override
  String get alertThresholdDesc => '当您的余额低于此金额时，您将收到通知。';

  @override
  String get daysInAdvance => '提前天数';

  @override
  String get daysInAdvanceDesc => '在余额用完前多少天发送提醒。';

  @override
  String get initialMeterReadingOptional => '这是可选的。您可以跳过此步骤，稍后添加您的第一个电表读数。';

  @override
  String errorLoadingSettings(String error) {
    return '加载设置时出错：$error';
  }
}
