import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:logging/logging.dart';
import 'dart:convert';

final paypalServiceProvider = Provider<PayPalService>((ref) {
  final storage = const FlutterSecureStorage();
  final client = http.Client();
  return PayPalService(storage, client);
});

class PayPalService {
  final FlutterSecureStorage storage;
  final http.Client client;

  PayPalService(this.storage, this.client);

  Future<void> initializePayPal(String clientId, String clientSecret) async {
    await storage.write(
      key: 'paypalClientId',
      value: base64Encode(utf8.encode(clientId)),
      aOptions: AndroidOptions(encryptedSharedPreferences: true),
      iOptions: IOSOptions(
        accessibility: KeychainAccessibility.unlocked,
        synchronizable: true,
      ),
    );
    await storage.write(
      key: 'paypalClientSecret',
      value: base64Encode(utf8.encode(clientSecret)),
      aOptions: const AndroidOptions(encryptedSharedPreferences: true),
      iOptions: const IOSOptions(
        accessibility: KeychainAccessibility.unlocked,
        synchronizable: true,
      ),
    );
  }

  Future<String> processDonation({
    required double amount,
    required String currency,
    required String description,
    required String userId,
  }) async {
    try {
      final clientIdBytes = await storage.read(key: 'paypalClientId');
      final clientSecretBytes = await storage.read(key: 'paypalClientSecret');

      if (clientIdBytes == null || clientSecretBytes == null) {
        throw Exception('PayPal credentials not initialized');
      }

      final clientId = utf8.decode(base64Decode(clientIdBytes));
      final clientSecret = utf8.decode(base64Decode(clientSecretBytes));

      final auth =
          'Basic ${base64Encode(utf8.encode('$clientId:$clientSecret'))}';

      // Create payment
      final createResponse = await client.post(
        Uri.parse('https://api.sandbox.paypal.com/v2/checkout/orders'),
        headers: {
          'Authorization': auth,
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'intent': 'CAPTURE',
          'purchase_units': [
            {
              'amount': {
                'currency_code': currency,
                'value': amount.toStringAsFixed(2),
                'description': description,
              },
            }
          ],
        }),
      );

      if (createResponse.statusCode != 201) {
        throw Exception(
            'Failed to create PayPal payment: ${createResponse.body}');
      }

      final Map<String, dynamic> createJson = jsonDecode(createResponse.body);
      final orderId = createJson['id'];

      // Register in app database
      final registerResponse = await client.post(
        Uri.parse('https://api.example.com/donations'),
        headers: {
          'Content-Type': 'application/json',
          'X-User-ID': userId,
        },
        body: jsonEncode({
          'paymentId': orderId,
          'amount': amount,
          'currency': currency,
          'status': 'created',
        }),
      );

      if (registerResponse.statusCode != 201) {
        throw Exception(
            'Failed to register donation: ${registerResponse.body}');
      }

      return orderId;
    } catch (e) {
      Logger('PayPalService').severe('Donation processing failed: $e');
      rethrow;
    }
  }
}
