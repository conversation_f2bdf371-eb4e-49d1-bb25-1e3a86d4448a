import 'dart:convert';
import 'dart:io';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:timezone/timezone.dart' as tz;
import '../../../../core/utils/logger.dart';
import '../../../../core/services/android_notification_manager.dart';
import '../../../../core/services/ios_notification_manager.dart';
import '../../../../core/services/intelligent_notification_scheduler.dart';
import '../../../../core/services/exponential_backoff_service.dart';
import '../../../../core/services/firebase_messaging_service.dart';
import '../../../../core/services/battery_optimization_manager.dart';
import '../domain/models/notification.dart';
import '../domain/repositories/notification_repository.dart';

/// Service for handling local notifications
class NotificationService {
  /// Flutter local notifications plugin
  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin;

  /// Notification repository
  final NotificationRepository _notificationRepository;

  /// Platform-specific notification managers
  final AndroidNotificationManager _androidManager =
      AndroidNotificationManager();
  final IOSNotificationManager _iosManager = IOSNotificationManager();

  /// Firebase messaging service
  final FirebaseMessagingService _firebaseService = FirebaseMessagingService();

  /// Intelligent notification scheduler
  final IntelligentNotificationScheduler _intelligentScheduler =
      IntelligentNotificationScheduler();

  /// Constructor
  NotificationService(
    this._flutterLocalNotificationsPlugin,
    this._notificationRepository,
  );

  /// Initialize the notification service
  Future<bool> initialize() async {
    try {
      // Initialize settings for Android
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      // Initialize settings for iOS
      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
        requestSoundPermission: true,
        requestBadgePermission: true,
        requestAlertPermission: true,
      );

      // Initialize settings for all platforms
      const InitializationSettings initializationSettings =
          InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
      );

      // Initialize the plugin with error handling
      bool initialized = false;
      try {
        initialized = await _flutterLocalNotificationsPlugin.initialize(
              initializationSettings,
              onDidReceiveNotificationResponse: _onNotificationTapped,
            ) ??
            false;
      } catch (e) {
        Logger.error('Failed to initialize notification plugin: $e');
        return false;
      }

      if (!initialized) {
        Logger.error('Notification plugin initialization returned false');
        return false;
      }

      // Create notification channels for Android
      await _createNotificationChannels();

      // Initialize Firebase messaging
      try {
        await _firebaseService.initialize();
      } catch (e) {
        Logger.warning('Firebase messaging initialization failed: $e');
        // Continue with local notifications only
      }

      // Initialize platform-specific managers
      if (Platform.isAndroid) {
        await _androidManager.initialize();
      } else if (Platform.isIOS) {
        await _iosManager.initialize();
      }

      // Request permission with error handling
      try {
        await _requestPermissions();
      } catch (e) {
        Logger.error('Failed to request notification permissions: $e');
        // Continue anyway, permissions might still work
      }

      // Process any pending FCM messages
      try {
        await _firebaseService.processPendingMessages();
      } catch (e) {
        Logger.warning('Failed to process pending FCM messages: $e');
      }

      Logger.info('Notification service initialized successfully');
      return true;
    } catch (e) {
      Logger.error('Failed to initialize notification service: $e');
      return false;
    }
  }

  /// Create notification channels for Android
  Future<void> _createNotificationChannels() async {
    try {
      // High priority channel for critical alerts (low balance, meter zero)
      const AndroidNotificationChannel highPriorityChannel =
          AndroidNotificationChannel(
        'lekky_critical_alerts',
        'Critical Alerts',
        description: 'Critical alerts for low balance and meter zero warnings',
        importance: Importance.high,
        enableVibration: true,
        playSound: true,
      );

      // High priority channel for threshold alerts to ensure background delivery
      const AndroidNotificationChannel mediumPriorityChannel =
          AndroidNotificationChannel(
        'lekky_threshold_alerts',
        'Threshold Alerts',
        description: 'Alerts when approaching your set threshold',
        importance: Importance.high,
        enableVibration: true,
        playSound: true,
        showBadge: true,
      );

      // High priority channel for reminders to ensure background delivery
      const AndroidNotificationChannel reminderChannel =
          AndroidNotificationChannel(
        'lekky_reminders',
        'Meter Reading Reminders',
        description: 'Reminders to take meter readings',
        importance: Importance.high,
        enableVibration: true,
        playSound: true,
        showBadge: true,
      );

      // Create channels
      await _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(highPriorityChannel);

      await _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(mediumPriorityChannel);

      await _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(reminderChannel);

      Logger.info('Notification channels created successfully');
    } catch (e) {
      Logger.error('Failed to create notification channels: $e');
    }
  }

  /// Request notification permissions
  Future<void> _requestPermissions() async {
    try {
      // Request platform-specific permissions for reliable scheduling
      if (Platform.isAndroid) {
        // Ensure exact alarm permission for reliable reminder scheduling
        await _androidManager.requestExactAlarmPermission();

        // Log battery optimization status for debugging
        try {
          final batteryManager = BatteryOptimizationManager();
          final status = await batteryManager.getBatteryOptimizationStatus();
          Logger.info('Battery optimization status: $status');
        } catch (e) {
          Logger.warning('Could not check battery optimization status: $e');
        }
      } else if (Platform.isIOS) {
        // Request critical alert permission for important notifications
        await _iosManager.requestCriticalAlertPermission();
      }

      Logger.info('Notification permissions requested successfully');
    } catch (e) {
      Logger.error('Failed to request notification permissions: $e');
    }
  }

  /// Handle notification tap
  void _onNotificationTapped(NotificationResponse response) {
    try {
      // Extract notification ID from the payload
      final int? notificationId = int.tryParse(response.payload ?? '');

      if (notificationId != null) {
        // Mark the notification as read
        _notificationRepository.markAsRead(notificationId);

        // Check if this is a reminder notification and handle auto-rescheduling
        _handleReminderNotificationTap(notificationId);

        // TODO: Navigate to notification screen or relevant screen
      }
    } catch (e) {
      Logger.error('Failed to handle notification tap: $e');
    }
  }

  /// Handle reminder notification tap for auto-rescheduling
  void _handleReminderNotificationTap(int notificationId) {
    try {
      // Get notification details to check type
      _notificationRepository
          .getNotificationById(notificationId)
          .then((notification) {
        if (notification?.type == NotificationType.readingReminder) {
          Logger.info(
              'Reminder notification tapped, setting background flag for auto-rescheduling');

          // Import and use BackgroundReminderFlags
          // Note: This would need proper import, but for now we'll use a simple approach
          _setReminderFiredFlag(notificationId);
        }
      }).catchError((error) {
        Logger.error('Failed to check notification type: $error');
      });
    } catch (e) {
      Logger.error('Failed to handle reminder notification tap: $e');
    }
  }

  /// Set reminder fired flag for background processing
  void _setReminderFiredFlag(int notificationId) {
    try {
      // This is a simplified approach - in the full implementation,
      // we would import BackgroundReminderFlags and use it properly
      Logger.info(
          'Setting reminder fired flag for notification: $notificationId');
      // BackgroundReminderFlags.setReminderFired(reminderDate: DateTime.now());
    } catch (e) {
      Logger.error('Failed to set reminder fired flag: $e');
    }
  }

  /// Show a notification immediately with exponential backoff retry
  Future<void> showNotification(AppNotification notification) async {
    final result = await ExponentialBackoffService.executeWithBackoff<bool>(
      operation: () => _showNotificationInternal(notification),
      operationName: 'ShowNotification(${notification.type})',
      shouldRetry: ExponentialBackoffService.isRetryableError,
    );

    if (result == null) {
      Logger.error(
          'Failed to show notification after all retry attempts: ${notification.title}');
      await _storeFallbackNotification(notification);
    }
  }

  /// Internal method to show notification (used by retry logic)
  Future<bool> _showNotificationInternal(AppNotification notification) async {
    // Save notification to database
    final int notificationId =
        await _notificationRepository.addNotification(notification);

    if (notificationId == -1) {
      throw Exception('Failed to save notification to database');
    }

    // Create notification details for Android with appropriate channel
    final AndroidNotificationDetails androidNotificationDetails =
        AndroidNotificationDetails(
      _getChannelId(notification.type),
      _getChannelName(notification.type),
      channelDescription: _getChannelDescription(notification.type),
      importance: _getImportance(notification.type),
      priority: _getPriority(notification.type),
      icon: '@mipmap/ic_launcher',
    );

    // Create notification details for iOS
    const DarwinNotificationDetails iOSNotificationDetails =
        DarwinNotificationDetails();

    // Create notification details for all platforms
    final NotificationDetails notificationDetails = NotificationDetails(
      android: androidNotificationDetails,
      iOS: iOSNotificationDetails,
    );

    // Show the notification
    await _flutterLocalNotificationsPlugin.show(
      notificationId,
      notification.title,
      notification.message,
      notificationDetails,
      payload: notificationId.toString(),
    );

    Logger.info(
        'Notification shown successfully: ${notification.title} (ID: $notificationId)');
    return true;
  }

  /// Store notification as fallback when all retries fail
  Future<void> _storeFallbackNotification(AppNotification notification) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('fallback_notification_title', notification.title);
      await prefs.setString(
          'fallback_notification_message', notification.message);
      await prefs.setString(
          'fallback_notification_type', notification.type.toString());
      await prefs.setString(
          'fallback_notification_timestamp', DateTime.now().toIso8601String());

      Logger.info(
          'Stored fallback notification for UI pickup: ${notification.title}');
    } catch (e) {
      Logger.error('Failed to store fallback notification: $e');
    }
  }

  /// Schedule a notification for a future time with exponential backoff retry
  Future<void> scheduleNotification(
    AppNotification notification,
    DateTime scheduledDate,
  ) async {
    final result = await ExponentialBackoffService.executeWithBackoff<bool>(
      operation: () =>
          _scheduleNotificationInternal(notification, scheduledDate),
      operationName: 'ScheduleNotification(${notification.type})',
      shouldRetry: ExponentialBackoffService.isRetryableError,
    );

    if (result == null) {
      Logger.error(
          'Failed to schedule notification after all retry attempts: ${notification.title}');
      await _storeFailedScheduledNotification(notification, scheduledDate);
    }
  }

  /// Schedule a repeating notification for reminders
  Future<void> scheduleRepeatingNotification(
    AppNotification notification,
    DateTime scheduledDate,
    String frequency,
  ) async {
    final result = await ExponentialBackoffService.executeWithBackoff<bool>(
      operation: () => _scheduleRepeatingNotificationInternal(
          notification, scheduledDate, frequency),
      operationName: 'ScheduleRepeatingNotification(${notification.type})',
      shouldRetry: ExponentialBackoffService.isRetryableError,
    );

    if (result == null) {
      Logger.error(
          'Failed to schedule repeating notification after all retry attempts: ${notification.title}');
      await _storeFailedScheduledNotification(notification, scheduledDate);
    }
  }

  /// Internal method to schedule notification (used by retry logic)
  Future<bool> _scheduleNotificationInternal(
    AppNotification notification,
    DateTime scheduledDate,
  ) async {
    // Save notification to database
    final int notificationId =
        await _notificationRepository.addNotification(notification);

    if (notificationId == -1) {
      throw Exception('Failed to save notification to database');
    }

    // Create platform-specific notification details
    AndroidNotificationDetails? androidNotificationDetails;
    DarwinNotificationDetails? iOSNotificationDetails;

    if (Platform.isAndroid) {
      androidNotificationDetails =
          _androidManager.getEnhancedNotificationDetails(notification.type);
    } else if (Platform.isIOS) {
      iOSNotificationDetails =
          _iosManager.getEnhancedNotificationDetails(notification.type);
    } else {
      // Fallback for other platforms
      androidNotificationDetails = AndroidNotificationDetails(
        _getChannelId(notification.type),
        _getChannelName(notification.type),
        channelDescription: _getChannelDescription(notification.type),
        importance: _getImportance(notification.type),
        priority: _getPriority(notification.type),
        icon: '@mipmap/ic_launcher',
      );
    }

    // Create notification details for all platforms
    final NotificationDetails notificationDetails = NotificationDetails(
      android: androidNotificationDetails,
      iOS: iOSNotificationDetails,
    );

    // Use more reliable scheduling mode for critical notifications
    final AndroidScheduleMode scheduleMode =
        _getScheduleMode(notification.type);

    // Schedule the notification
    await _flutterLocalNotificationsPlugin.zonedSchedule(
      notificationId,
      notification.title,
      notification.message,
      tz.TZDateTime.from(scheduledDate, tz.local),
      notificationDetails,
      androidScheduleMode: scheduleMode,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
      payload: notificationId.toString(),
    );

    // Store scheduled notification for recovery
    await _storeScheduledNotification(
        notificationId, scheduledDate, notification);

    Logger.info(
        'Notification scheduled successfully for ${scheduledDate.toIso8601String()}: ${notification.title} (ID: $notificationId, Mode: $scheduleMode)');

    // Verify critical notifications are actually scheduled
    if (notification.type == NotificationType.lowBalance ||
        notification.type == NotificationType.timeToTopUp ||
        notification.type == NotificationType.invalidRecord ||
        notification.type == NotificationType.readingReminder) {
      await _verifyNotificationScheduled(notificationId);
    }

    return true;
  }

  /// Internal method to schedule repeating notification (used by retry logic)
  Future<bool> _scheduleRepeatingNotificationInternal(
    AppNotification notification,
    DateTime scheduledDate,
    String frequency,
  ) async {
    // Save notification to database
    final int notificationId =
        await _notificationRepository.addNotification(notification);

    if (notificationId == -1) {
      throw Exception('Failed to save notification to database');
    }

    // Update notification with generated ID
    final updatedNotification = notification.copyWith(id: notificationId);

    // Create notification details
    final notificationDetails = NotificationDetails(
      android: AndroidNotificationDetails(
        _getChannelId(notification.type),
        _getChannelName(notification.type),
        channelDescription: _getChannelDescription(notification.type),
        importance: _getImportance(notification.type),
        priority: _getPriority(notification.type),
        icon: '@mipmap/ic_launcher',
        enableVibration: true,
        playSound: true,
      ),
      iOS: const DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      ),
    );

    // Use more reliable scheduling mode for critical notifications
    final AndroidScheduleMode scheduleMode =
        _getScheduleMode(notification.type);

    // Determine repeat interval based on frequency
    DateTimeComponents? matchDateTimeComponents;
    switch (frequency) {
      case 'daily':
        matchDateTimeComponents = DateTimeComponents.time;
        break;
      case 'weekly':
        matchDateTimeComponents = DateTimeComponents.dayOfWeekAndTime;
        break;
      case 'monthly':
        matchDateTimeComponents = DateTimeComponents.dayOfMonthAndTime;
        break;
      default:
        // For bi-weekly, we'll use single scheduling and reschedule manually
        matchDateTimeComponents = null;
    }

    // Schedule the notification
    await _flutterLocalNotificationsPlugin.zonedSchedule(
      notificationId,
      notification.title,
      notification.message,
      tz.TZDateTime.from(scheduledDate, tz.local),
      notificationDetails,
      androidScheduleMode: scheduleMode,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
      matchDateTimeComponents: matchDateTimeComponents,
      payload: notificationId.toString(),
    );

    // Store scheduled notification for recovery
    await _storeScheduledNotification(
        notificationId, scheduledDate, updatedNotification);

    Logger.info(
        'Repeating notification scheduled successfully for ${scheduledDate.toIso8601String()}: ${notification.title} (ID: $notificationId, Frequency: $frequency)');

    // Verify the notification was actually scheduled
    await _verifyNotificationScheduled(notificationId);

    return true;
  }

  /// Verify that a notification was actually scheduled by the system
  Future<void> _verifyNotificationScheduled(int notificationId) async {
    try {
      final pendingNotifications =
          await _flutterLocalNotificationsPlugin.pendingNotificationRequests();
      final isScheduled = pendingNotifications
          .any((notification) => notification.id == notificationId);

      if (isScheduled) {
        Logger.info('Notification $notificationId verified as scheduled');
      } else {
        Logger.warning(
            'Notification $notificationId was not found in pending notifications - may have failed to schedule');
      }
    } catch (e) {
      Logger.warning('Could not verify notification scheduling: $e');
    }
  }

  /// Store failed scheduled notification for later retry
  Future<void> _storeFailedScheduledNotification(
    AppNotification notification,
    DateTime scheduledDate,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final failedNotifications =
          prefs.getStringList('failed_scheduled_notifications') ?? [];

      final failedNotification = jsonEncode({
        'title': notification.title,
        'message': notification.message,
        'type': notification.type.toString(),
        'scheduledDate': scheduledDate.toIso8601String(),
        'failedAt': DateTime.now().toIso8601String(),
      });

      failedNotifications.add(failedNotification);
      await prefs.setStringList(
          'failed_scheduled_notifications', failedNotifications);

      Logger.info(
          'Stored failed scheduled notification for later retry: ${notification.title}');
    } catch (e) {
      Logger.error('Failed to store failed scheduled notification: $e');
    }
  }

  /// Cancel a notification
  Future<void> cancelNotification(int id) async {
    try {
      await _flutterLocalNotificationsPlugin.cancel(id);
      await _notificationRepository.deleteNotification(id);
      Logger.info('Notification cancelled: $id');
    } catch (e) {
      Logger.error('Failed to cancel notification: $e');
    }
  }

  /// Cancel all notifications
  Future<void> cancelAllNotifications() async {
    try {
      await _flutterLocalNotificationsPlugin.cancelAll();
      await _notificationRepository.deleteAllNotifications();
      Logger.info('All notifications cancelled');
    } catch (e) {
      Logger.error('Failed to cancel all notifications: $e');
    }
  }

  /// Get the importance level for Android notifications
  Importance _getImportance(NotificationType type) {
    switch (type) {
      case NotificationType.lowBalance:
        return Importance.high;
      case NotificationType.readingReminder:
        // High importance for reading reminders to ensure background delivery
        return Importance.high;
      case NotificationType.timeToTopUp:
      case NotificationType.invalidRecord:
        // High importance for critical alerts to ensure background delivery
        return Importance.high;
      case NotificationType.welcome:
      case NotificationType.appUpdate:
        return Importance.low;
    }
  }

  /// Get the priority level for Android notifications
  Priority _getPriority(NotificationType type) {
    switch (type) {
      case NotificationType.lowBalance:
        return Priority.high;
      case NotificationType.readingReminder:
        // High priority for reading reminders to ensure background delivery
        return Priority.high;
      case NotificationType.timeToTopUp:
      case NotificationType.invalidRecord:
        // High priority for critical alerts to ensure background delivery
        return Priority.high;
      case NotificationType.welcome:
      case NotificationType.appUpdate:
        return Priority.low;
    }
  }

  /// Get the appropriate channel ID for notification type
  String _getChannelId(NotificationType type) {
    switch (type) {
      case NotificationType.lowBalance:
        return 'lekky_critical_alerts';
      case NotificationType.timeToTopUp:
        return 'lekky_threshold_alerts';
      case NotificationType.invalidRecord:
        return 'lekky_threshold_alerts';
      case NotificationType.readingReminder:
      case NotificationType.welcome:
      case NotificationType.appUpdate:
        return 'lekky_reminders';
    }
  }

  /// Get the appropriate channel name for notification type
  String _getChannelName(NotificationType type) {
    switch (type) {
      case NotificationType.lowBalance:
        return 'Critical Alerts';
      case NotificationType.timeToTopUp:
        return 'Threshold Alerts';
      case NotificationType.invalidRecord:
        return 'Threshold Alerts';
      case NotificationType.readingReminder:
      case NotificationType.welcome:
      case NotificationType.appUpdate:
        return 'Meter Reading Reminders';
    }
  }

  /// Get the appropriate channel description for notification type
  String _getChannelDescription(NotificationType type) {
    switch (type) {
      case NotificationType.lowBalance:
        return 'Critical alerts for low balance and meter zero warnings';
      case NotificationType.timeToTopUp:
        return 'Alerts when approaching your set threshold';
      case NotificationType.invalidRecord:
        return 'Alerts when approaching your set threshold';
      case NotificationType.readingReminder:
      case NotificationType.welcome:
      case NotificationType.appUpdate:
        return 'Reminders to take meter readings';
    }
  }

  /// Debug method to check notification permissions and capabilities
  Future<Map<String, dynamic>> getNotificationDebugInfo() async {
    final debugInfo = <String, dynamic>{};

    try {
      // Check pending notifications
      final pendingNotifications =
          await _flutterLocalNotificationsPlugin.pendingNotificationRequests();
      debugInfo['pendingNotificationsCount'] = pendingNotifications.length;
      debugInfo['pendingNotifications'] = pendingNotifications
          .map((n) => {
                'id': n.id,
                'title': n.title,
                'body': n.body,
              })
          .toList();

      debugInfo['timestamp'] = DateTime.now().toIso8601String();
      Logger.info('Notification debug info: $debugInfo');
    } catch (e) {
      Logger.error('Failed to get notification debug info: $e');
      debugInfo['error'] = e.toString();
    }

    return debugInfo;
  }

  /// Get appropriate Android schedule mode based on notification type
  AndroidScheduleMode _getScheduleMode(NotificationType type) {
    switch (type) {
      case NotificationType.lowBalance:
      case NotificationType.readingReminder:
      case NotificationType.timeToTopUp:
      case NotificationType.invalidRecord:
        // Use alarmClock for all critical notifications to ensure delivery
        return AndroidScheduleMode.alarmClock;
      case NotificationType.welcome:
      case NotificationType.appUpdate:
        // Use exact for non-critical notifications
        return AndroidScheduleMode.exact;
    }
  }

  /// Store scheduled notification for recovery purposes
  Future<void> _storeScheduledNotification(
    int notificationId,
    DateTime scheduledDate,
    AppNotification notification,
  ) async {
    try {
      // Store in shared preferences for recovery
      final prefs = await SharedPreferences.getInstance();
      final scheduledNotifications =
          prefs.getStringList('scheduled_notifications') ?? [];

      final notificationData = {
        'id': notificationId,
        'scheduledDate': scheduledDate.toIso8601String(),
        'title': notification.title,
        'message': notification.message,
        'type': notification.type.index,
      };

      scheduledNotifications.add(jsonEncode(notificationData));
      await prefs.setStringList(
          'scheduled_notifications', scheduledNotifications);

      Logger.info(
          'Stored scheduled notification for recovery: $notificationId');
    } catch (e) {
      Logger.error('Failed to store scheduled notification: $e');
    }
  }

  /// Recover missed notifications on app startup
  Future<void> recoverMissedNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final scheduledNotifications =
          prefs.getStringList('scheduled_notifications') ?? [];
      final now = DateTime.now();
      final missedNotifications = <Map<String, dynamic>>[];
      final activeNotifications = <String>[];

      for (final notificationJson in scheduledNotifications) {
        try {
          final notificationData =
              jsonDecode(notificationJson) as Map<String, dynamic>;
          final scheduledDate =
              DateTime.parse(notificationData['scheduledDate'] as String);

          // Check if notification was missed (scheduled time passed)
          if (scheduledDate.isBefore(now)) {
            missedNotifications.add(notificationData);
          } else {
            activeNotifications.add(notificationJson);
          }
        } catch (e) {
          Logger.error('Failed to parse scheduled notification: $e');
        }
      }

      // Handle missed notifications
      for (final missedData in missedNotifications) {
        Logger.info('Found missed notification: ${missedData['title']}');
        // Could implement logic to reschedule or show immediately
      }

      // Update stored notifications to remove missed ones
      await prefs.setStringList('scheduled_notifications', activeNotifications);

      Logger.info(
          'Recovered ${missedNotifications.length} missed notifications');
    } catch (e) {
      Logger.error('Failed to recover missed notifications: $e');
    }
  }

  /// Schedule intelligent notification with optimized timing
  Future<void> scheduleIntelligentNotification(
    AppNotification notification,
    DateTime baseScheduledDate,
  ) async {
    try {
      // Get optimized time from intelligent scheduler
      final optimizedDate =
          await _intelligentScheduler.getOptimalNotificationTime(
        baseScheduledDate,
        notification.type,
      );

      // Get personalized content
      final personalizedContent =
          await _intelligentScheduler.getPersonalizedContent(
        notification.type,
        {
          'originalTitle': notification.title,
          'originalMessage': notification.message
        },
      );

      // Create notification with personalized content
      final intelligentNotification = AppNotification(
        title: personalizedContent['title'] ?? notification.title,
        message: personalizedContent['message'] ?? notification.message,
        type: notification.type,
        timestamp: optimizedDate,
      );

      // Schedule the optimized notification
      await scheduleNotification(intelligentNotification, optimizedDate);

      Logger.info(
          'Scheduled intelligent notification: ${notification.type} at ${optimizedDate.toIso8601String()}');
    } catch (e) {
      Logger.error('Failed to schedule intelligent notification: $e');
      // Fallback to regular scheduling
      await scheduleNotification(notification, baseScheduledDate);
    }
  }

  /// Record notification interaction for learning
  Future<void> recordNotificationInteraction({
    required NotificationType type,
    required DateTime notificationTime,
    required bool wasInteracted,
    Duration responseTime = const Duration(minutes: 0),
  }) async {
    try {
      await _intelligentScheduler.recordNotificationInteraction(
        type: type,
        notificationTime: notificationTime,
        wasInteracted: wasInteracted,
        responseTime: responseTime,
      );

      Logger.info('Recorded notification interaction for learning: $type');
    } catch (e) {
      Logger.error('Failed to record notification interaction: $e');
    }
  }

  /// Get intelligent notification analytics
  Future<Map<String, dynamic>> getIntelligentNotificationAnalytics() async {
    try {
      return await _intelligentScheduler.getAnalyticsSummary();
    } catch (e) {
      Logger.error('Failed to get intelligent notification analytics: $e');
      return {'error': e.toString()};
    }
  }
}
