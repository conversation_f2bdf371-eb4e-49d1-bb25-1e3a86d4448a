import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';
import '../di/service_locator.dart';
import '../../features/notifications/domain/models/notification.dart';
import '../../features/notifications/data/notification_service.dart';
import 'exponential_backoff_service.dart';

/// Service for managing notification queue and retry logic
class NotificationQueueManager {
  static final NotificationQueueManager _instance =
      NotificationQueueManager._internal();
  factory NotificationQueueManager() => _instance;
  NotificationQueueManager._internal();

  static const String _fallbackNotificationsKey =
      'fallback_notifications_queue';
  static const String _failedScheduledNotificationsKey =
      'failed_scheduled_notifications';

  static const int _maxQueueSize = 50;

  /// Process all queued notifications with retry logic
  Future<void> processQueue() async {
    try {
      Logger.info('NotificationQueueManager: Processing notification queue');

      await _processFallbackNotifications();
      await _processFailedScheduledNotifications();
      await _cleanupOldEntries();

      Logger.info('NotificationQueueManager: Queue processing completed');
    } catch (e) {
      Logger.error('NotificationQueueManager: Error processing queue: $e');
    }
  }

  /// Process fallback notifications that failed to show
  Future<void> _processFallbackNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Check for single fallback notification
      final title = prefs.getString('fallback_notification_title');
      if (title != null) {
        await _retryFallbackNotification(prefs);
      }

      // Check for queued fallback notifications
      final queuedNotifications =
          prefs.getStringList(_fallbackNotificationsKey) ?? [];
      if (queuedNotifications.isNotEmpty) {
        await _retryQueuedFallbackNotifications(queuedNotifications);
      }
    } catch (e) {
      Logger.error(
          'NotificationQueueManager: Error processing fallback notifications: $e');
    }
  }

  /// Retry single fallback notification
  Future<void> _retryFallbackNotification(SharedPreferences prefs) async {
    try {
      final title = prefs.getString('fallback_notification_title');
      final message = prefs.getString('fallback_notification_message');
      final typeString = prefs.getString('fallback_notification_type');
      final timestampString =
          prefs.getString('fallback_notification_timestamp');

      if (title == null || message == null || typeString == null) return;

      final type = NotificationType.values.firstWhere(
        (e) => e.toString() == typeString,
        orElse: () => NotificationType.welcome,
      );

      final notification = AppNotification(
        title: title,
        message: message,
        type: type,
        timestamp: timestampString != null
            ? DateTime.parse(timestampString)
            : DateTime.now(),
      );

      final result = await ExponentialBackoffService.executeWithBackoff<bool>(
        operation: () => _attemptNotificationRetry(notification),
        operationName: 'RetryFallbackNotification',
        maxAttempts: 3,
        shouldRetry: ExponentialBackoffService.isRetryableError,
      );

      if (result == true) {
        // Clear fallback notification on success
        await prefs.remove('fallback_notification_title');
        await prefs.remove('fallback_notification_message');
        await prefs.remove('fallback_notification_type');
        await prefs.remove('fallback_notification_timestamp');

        Logger.info(
            'NotificationQueueManager: Successfully retried fallback notification');
      } else {
        // Move to queue for later retry
        await _addToFallbackQueue(notification);
        Logger.warning(
            'NotificationQueueManager: Failed to retry fallback notification, added to queue');
      }
    } catch (e) {
      Logger.error(
          'NotificationQueueManager: Error retrying fallback notification: $e');
    }
  }

  /// Retry queued fallback notifications
  Future<void> _retryQueuedFallbackNotifications(
      List<String> queuedNotifications) async {
    final prefs = await SharedPreferences.getInstance();
    final successfulRetries = <String>[];

    for (final notificationJson in queuedNotifications) {
      try {
        final notificationData =
            jsonDecode(notificationJson) as Map<String, dynamic>;
        final notification = _parseQueuedNotification(notificationData);

        if (notification == null) continue;

        // Check if notification is too old (older than 24 hours)
        final age = DateTime.now().difference(notification.timestamp);
        if (age.inHours > 24) {
          successfulRetries.add(notificationJson);
          continue;
        }

        final result = await ExponentialBackoffService.executeWithBackoff<bool>(
          operation: () => _attemptNotificationRetry(notification),
          operationName: 'RetryQueuedNotification',
          maxAttempts: 2,
          shouldRetry: ExponentialBackoffService.isRetryableError,
        );

        if (result == true) {
          successfulRetries.add(notificationJson);
          Logger.info(
              'NotificationQueueManager: Successfully retried queued notification: ${notification.title}');
        }
      } catch (e) {
        Logger.error(
            'NotificationQueueManager: Error retrying queued notification: $e');
      }
    }

    // Remove successful retries from queue
    if (successfulRetries.isNotEmpty) {
      final remainingNotifications = queuedNotifications
          .where((notification) => !successfulRetries.contains(notification))
          .toList();

      await prefs.setStringList(
          _fallbackNotificationsKey, remainingNotifications);
      Logger.info(
          'NotificationQueueManager: Removed ${successfulRetries.length} successful retries from queue');
    }
  }

  /// Process failed scheduled notifications
  Future<void> _processFailedScheduledNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final failedNotifications =
          prefs.getStringList(_failedScheduledNotificationsKey) ?? [];

      if (failedNotifications.isEmpty) return;

      final successfulRetries = <String>[];

      for (final notificationJson in failedNotifications) {
        try {
          final notificationData =
              jsonDecode(notificationJson) as Map<String, dynamic>;
          final scheduledDate =
              DateTime.parse(notificationData['scheduledDate'] as String);

          // Skip if scheduled time has passed
          if (scheduledDate.isBefore(DateTime.now())) {
            successfulRetries.add(notificationJson);
            continue;
          }

          final notification = _parseQueuedNotification(notificationData);
          if (notification == null) continue;

          final notificationService =
              await serviceLocator.getAsync<NotificationService>();

          final result =
              await ExponentialBackoffService.executeWithBackoff<bool>(
            operation: () => _attemptScheduleRetry(
                notificationService, notification, scheduledDate),
            operationName: 'RetryScheduledNotification',
            maxAttempts: 2,
            shouldRetry: ExponentialBackoffService.isRetryableError,
          );

          if (result == true) {
            successfulRetries.add(notificationJson);
            Logger.info(
                'NotificationQueueManager: Successfully rescheduled notification: ${notification.title}');
          }
        } catch (e) {
          Logger.error(
              'NotificationQueueManager: Error retrying scheduled notification: $e');
        }
      }

      // Remove successful retries from failed queue
      if (successfulRetries.isNotEmpty) {
        final remainingNotifications = failedNotifications
            .where((notification) => !successfulRetries.contains(notification))
            .toList();

        await prefs.setStringList(
            _failedScheduledNotificationsKey, remainingNotifications);
        Logger.info(
            'NotificationQueueManager: Removed ${successfulRetries.length} successful scheduled retries');
      }
    } catch (e) {
      Logger.error(
          'NotificationQueueManager: Error processing failed scheduled notifications: $e');
    }
  }

  /// Attempt to retry showing a notification
  Future<bool> _attemptNotificationRetry(AppNotification notification) async {
    try {
      final notificationService =
          await serviceLocator.getAsync<NotificationService>();
      await notificationService.showNotification(notification);
      return true;
    } catch (e) {
      Logger.warning('NotificationQueueManager: Notification retry failed: $e');
      return false;
    }
  }

  /// Attempt to retry scheduling a notification
  Future<bool> _attemptScheduleRetry(
    NotificationService notificationService,
    AppNotification notification,
    DateTime scheduledDate,
  ) async {
    try {
      await notificationService.scheduleNotification(
          notification, scheduledDate);
      return true;
    } catch (e) {
      Logger.warning('NotificationQueueManager: Schedule retry failed: $e');
      return false;
    }
  }

  /// Add notification to fallback queue
  Future<void> _addToFallbackQueue(AppNotification notification) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final queuedNotifications =
          prefs.getStringList(_fallbackNotificationsKey) ?? [];

      // Limit queue size
      if (queuedNotifications.length >= _maxQueueSize) {
        queuedNotifications.removeAt(0); // Remove oldest
      }

      final notificationJson = jsonEncode({
        'title': notification.title,
        'message': notification.message,
        'type': notification.type.toString(),
        'timestamp': notification.timestamp.toIso8601String(),
        'queuedAt': DateTime.now().toIso8601String(),
      });

      queuedNotifications.add(notificationJson);
      await prefs.setStringList(_fallbackNotificationsKey, queuedNotifications);

      Logger.info(
          'NotificationQueueManager: Added notification to fallback queue: ${notification.title}');
    } catch (e) {
      Logger.error(
          'NotificationQueueManager: Error adding to fallback queue: $e');
    }
  }

  /// Parse queued notification from JSON data
  AppNotification? _parseQueuedNotification(Map<String, dynamic> data) {
    try {
      final typeString = data['type'] as String;
      final type = NotificationType.values.firstWhere(
        (e) => e.toString() == typeString,
        orElse: () => NotificationType.welcome,
      );

      return AppNotification(
        title: data['title'] as String,
        message: data['message'] as String,
        type: type,
        timestamp: DateTime.parse(data['timestamp'] as String),
      );
    } catch (e) {
      Logger.error(
          'NotificationQueueManager: Error parsing queued notification: $e');
      return null;
    }
  }

  /// Clean up old entries from queues
  Future<void> _cleanupOldEntries() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now();

      // Clean up fallback queue
      final fallbackNotifications =
          prefs.getStringList(_fallbackNotificationsKey) ?? [];
      final validFallbackNotifications =
          fallbackNotifications.where((notificationJson) {
        try {
          final data = jsonDecode(notificationJson) as Map<String, dynamic>;
          final queuedAt = DateTime.parse(data['queuedAt'] as String);
          return now.difference(queuedAt).inHours < 48; // Keep for 48 hours
        } catch (e) {
          return false; // Remove invalid entries
        }
      }).toList();

      if (validFallbackNotifications.length != fallbackNotifications.length) {
        await prefs.setStringList(
            _fallbackNotificationsKey, validFallbackNotifications);
        Logger.info(
            'NotificationQueueManager: Cleaned up ${fallbackNotifications.length - validFallbackNotifications.length} old fallback entries');
      }

      // Clean up failed scheduled notifications
      final failedScheduled =
          prefs.getStringList(_failedScheduledNotificationsKey) ?? [];
      final validFailedScheduled = failedScheduled.where((notificationJson) {
        try {
          final data = jsonDecode(notificationJson) as Map<String, dynamic>;
          final failedAt = DateTime.parse(data['failedAt'] as String);
          return now.difference(failedAt).inHours < 24; // Keep for 24 hours
        } catch (e) {
          return false; // Remove invalid entries
        }
      }).toList();

      if (validFailedScheduled.length != failedScheduled.length) {
        await prefs.setStringList(
            _failedScheduledNotificationsKey, validFailedScheduled);
        Logger.info(
            'NotificationQueueManager: Cleaned up ${failedScheduled.length - validFailedScheduled.length} old failed scheduled entries');
      }
    } catch (e) {
      Logger.error(
          'NotificationQueueManager: Error cleaning up old entries: $e');
    }
  }

  /// Get queue status for diagnostics
  Future<Map<String, dynamic>> getQueueStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final fallbackCount =
          (prefs.getStringList(_fallbackNotificationsKey) ?? []).length;
      final failedScheduledCount =
          (prefs.getStringList(_failedScheduledNotificationsKey) ?? []).length;
      final hasSingleFallback =
          prefs.getString('fallback_notification_title') != null;

      return {
        'fallbackQueueCount': fallbackCount,
        'failedScheduledCount': failedScheduledCount,
        'hasSingleFallback': hasSingleFallback,
        'totalPending':
            fallbackCount + failedScheduledCount + (hasSingleFallback ? 1 : 0),
        'lastProcessed': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      Logger.error('NotificationQueueManager: Error getting queue status: $e');
      return {'error': e.toString()};
    }
  }
}
