plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
}

// Load keystore properties with secure fallback
def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file("keystore.properties")
def hasKeystore = false

if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
    hasKeystore = true
    println "✓ Keystore configuration loaded"
} else {
    println "⚠ Warning: keystore.properties not found. Release builds will use debug signing."
    println "  Copy keystore.properties.template to keystore.properties and configure for production."

    // Fallback to environment variables for CI/CD
    keystoreProperties['storeFile'] = System.getenv('KEYSTORE_FILE') ?: 'debug.keystore'
    keystoreProperties['storePassword'] = System.getenv('KEYSTORE_PASSWORD') ?: 'android'
    keystoreProperties['keyAlias'] = System.getenv('KEY_ALIAS') ?: 'androiddebugkey'
    keystoreProperties['keyPassword'] = System.getenv('KEY_PASSWORD') ?: 'android'
}

// Load local properties for versioning
def localProperties = new Properties()
def localPropertiesFile = rootProject.file("local.properties")
if (localPropertiesFile.exists()) {
    localProperties.load(new FileInputStream(localPropertiesFile))
}

def flutterVersionCode = localProperties.getProperty("flutter.versionCode") ?: "1"
def flutterVersionName = localProperties.getProperty("flutter.versionName") ?: "1.0"

android {
    namespace "com.lekky.app"
    compileSdk = 34
    ndkVersion = "25.1.8937393" // Or: flutter.ndkVersion

    defaultConfig {
        applicationId "com.lekky.app"
        minSdk = 21
        targetSdk = 34
        versionCode = flutterVersionCode.toInteger()
        versionName = flutterVersionName
    }

    signingConfigs {
        release {
            if (hasKeystore) {
                storeFile file(keystoreProperties['storeFile'])
                storePassword keystoreProperties['storePassword']
                keyAlias keystoreProperties['keyAlias']
                keyPassword keystoreProperties['keyPassword']
            } else {
                // Fallback to debug signing for development
                storeFile file("debug.keystore")
                storePassword "android"
                keyAlias "androiddebugkey"
                keyPassword "android"
            }
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            shrinkResources true
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = "1.8"
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }
}

flutter {
    source '../..'
}

dependencies {}

// Security Notes:
// - keystore.properties is excluded from version control for security
// - See SECURITY_SETUP.md for proper keystore configuration
// - Use environment variables for CI/CD deployments
