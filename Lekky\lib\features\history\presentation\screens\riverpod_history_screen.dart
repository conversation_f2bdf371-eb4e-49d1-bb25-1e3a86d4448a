import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/history_provider.dart';
import '../widgets/history_pagination.dart';
import '../widgets/history_entry_item.dart';
import '../widgets/history_empty_state.dart';
import '../dialogs/history_filter_dialog.dart';
import '../../../../core/providers/preference_provider.dart';
import '../../../../core/widgets/app_banner.dart';
import '../../../../core/widgets/lekky_button.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../validation/presentation/dialogs/validation_dialog.dart';
import '../../../validation/presentation/widgets/validation_icon_widget.dart';
import '../../../entries/presentation/dialogs/riverpod_add_entry_dialog.dart';
import '../../domain/models/history_state.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// Riverpod-based History screen demonstrating the migration pattern
/// This shows how to use the new HistoryProvider with smart pagination
class RiverpodHistoryScreen extends ConsumerStatefulWidget {
  const RiverpodHistoryScreen({super.key});

  @override
  ConsumerState<RiverpodHistoryScreen> createState() =>
      _RiverpodHistoryScreenState();
}

class _RiverpodHistoryScreenState extends ConsumerState<RiverpodHistoryScreen> {
  late ScrollController _historyScrollController;
  late ScrollController _scrollToTopController;

  @override
  void initState() {
    super.initState();
    _historyScrollController = ScrollController();
    _scrollToTopController = ScrollController();
  }

  @override
  void dispose() {
    _historyScrollController.dispose();
    _scrollToTopController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final historyAsync = ref.watch(historyProvider);
    final preferencesAsync = ref.watch(preferencesProvider);

    return Scaffold(
      body: Column(
        children: [
          // App Banner with filter toggle icon
          LayoutBuilder(
            builder: (context, constraints) {
              // Calculate filter icon position to align with History text
              final mediaQuery = MediaQuery.of(context);
              final safeAreaTop = mediaQuery.padding.top;
              const bannerVerticalPadding = 12.0;
              const textHeight = 28.0;
              const textLineHeight = 1.2;
              const iconSize = 28.0;

              // Calculate text center position
              final textCenterY = safeAreaTop +
                  bannerVerticalPadding +
                  (textHeight * textLineHeight) / 2;
              // Position icon center to align with text center
              final iconTopPosition = textCenterY - (iconSize / 2);

              return Stack(
                children: [
                  AppBanner(
                    message: AppLocalizations.of(context).history,
                    gradientColors: AppColors.getHistoryMainCardGradient(
                        Theme.of(context).brightness == Brightness.dark),
                    textColor: AppColors.getAppBarTextColor('history',
                        Theme.of(context).brightness == Brightness.dark),
                  ),
                  // Entry Validation icon positioned left of filter icon
                  Positioned(
                    top: iconTopPosition,
                    right:
                        68, // 16 (banner padding) + 28 (filter icon) + 24 (spacing)
                    child: ValidationIconWidget(
                      onTap: () => showValidationDialog(context),
                    ),
                  ),
                  // Filter toggle icon positioned to align with History text
                  Positioned(
                    top: iconTopPosition,
                    right: 16,
                    child: historyAsync.when(
                      data: (historyState) => IconButton(
                        icon: Icon(
                          Icons.tune,
                          color: AppColors.getFilterIconColor(
                              ref
                                  .read(historyProvider.notifier)
                                  .hasActiveFilters(historyState),
                              Theme.of(context).brightness == Brightness.dark),
                          size: 28,
                        ),
                        onPressed: () =>
                            _showFilterDialog(context, ref, historyState),
                        tooltip: ref
                                .read(historyProvider.notifier)
                                .hasActiveFilters(historyState)
                            ? 'Filters applied'
                            : 'Filter entries',
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                      loading: () => const SizedBox.shrink(),
                      error: (_, __) => const SizedBox.shrink(),
                    ),
                  ),
                ],
              );
            },
          ),

          // Main content based on async state
          Expanded(
            child: historyAsync.when(
              data: (historyState) => _buildHistoryContent(
                context,
                ref,
                historyState,
                preferencesAsync,
              ),
              loading: () => const Center(
                child: CircularProgressIndicator(),
              ),
              error: (error, stackTrace) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Theme.of(context).colorScheme.error,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Failed to load history',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      error.toString(),
                      style: Theme.of(context).textTheme.bodyMedium,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => ref.refresh(historyProvider),
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build the main history content
  Widget _buildHistoryContent(
    BuildContext context,
    WidgetRef ref,
    HistoryState historyState,
    AsyncValue<dynamic> preferencesAsync,
  ) {
    return Column(
      children: [
        // Main content area
        Expanded(
          child:
              _buildMainContent(context, ref, historyState, preferencesAsync),
        ),

        // Pagination - Fixed: Now shows based on totalPages, not current entries
        if (historyState.needsPagination)
          HistoryPagination(
            currentPage: historyState.currentPage,
            totalPages: historyState.totalPages,
            onPreviousPage: () {
              ref.read(historyProvider.notifier).previousPage();
            },
            onNextPage: () {
              ref.read(historyProvider.notifier).nextPage();
            },
            onPageSelected: (page) {
              ref.read(historyProvider.notifier).goToPage(page);
            },
            onFirstPage: () {
              ref.read(historyProvider.notifier).goToFirstPage();
            },
            onLastPage: () {
              ref.read(historyProvider.notifier).goToLastPage();
            },
          ),

        // Action buttons row
        _buildActionButtonsRow(context, ref, historyState),
      ],
    );
  }

  /// Build the main content (entries list or empty state)
  Widget _buildMainContent(
    BuildContext context,
    WidgetRef ref,
    HistoryState historyState,
    AsyncValue<dynamic> preferencesAsync,
  ) {
    if (historyState.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (historyState.hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Error: ${historyState.errorMessage}',
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref.read(historyProvider.notifier).refresh(),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (!historyState.hasEntries) {
      return HistoryEmptyState(
        filterType: historyState.filterType,
        hasDateFilter:
            historyState.startDate != null || historyState.endDate != null,
        onClearFilters: () => _clearFilters(ref),
      );
    }

    // Build entries list with RefreshIndicator and custom scrollbar
    return RefreshIndicator(
      onRefresh: () => ref.read(historyProvider.notifier).refresh(),
      child: preferencesAsync.when(
        data: (preferences) => _buildScrollableList(historyState, preferences),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, _) => Center(child: Text('Error: $error')),
      ),
    );
  }

  /// Build scrollable list with custom scrollbar and smooth scroll animations
  Widget _buildScrollableList(HistoryState historyState, dynamic preferences) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Stack(
      children: [
        // Main scrollable list with custom scrollbar
        RawScrollbar(
          controller: _historyScrollController,
          thumbVisibility: true,
          thickness: 6,
          thumbColor: isDarkMode
              ? Colors.grey.withOpacity(0.9)
              : Colors.grey.withOpacity(0.8),
          radius: const Radius.circular(3.0),
          padding: const EdgeInsets.only(right: 2),
          child: ListView.builder(
            controller: _historyScrollController,
            physics: const AlwaysScrollableScrollPhysics(),
            itemCount: historyState.entries.length,
            itemBuilder: (context, index) {
              final entry = historyState.entries[index];
              return HistoryEntryItem(
                entry: entry,
                currencySymbol: preferences.currencySymbol,
                onEntryUpdated: () =>
                    ref.read(historyProvider.notifier).refresh(),
                onEntryDeleted: () =>
                    ref.read(historyProvider.notifier).refresh(),
              );
            },
          ),
        ),

        // Auto-refresh loading indicator
        if (historyState.isLoading)
          Positioned(
            top: 16,
            right: 16,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: isDarkMode
                    ? Colors.black.withOpacity(0.7)
                    : Colors.white.withOpacity(0.9),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    isDarkMode ? Colors.white : Colors.grey[600]!,
                  ),
                ),
              ),
            ),
          ),

        // Scroll-to-top button (appears when scrolled down)
        Positioned(
          bottom: 16,
          right: 16,
          child: AnimatedBuilder(
            animation: _historyScrollController,
            builder: (context, child) {
              final showButton = _historyScrollController.hasClients &&
                  _historyScrollController.offset > 200;

              return AnimatedOpacity(
                opacity: showButton ? 1.0 : 0.0,
                duration: const Duration(milliseconds: 300),
                child: showButton
                    ? FloatingActionButton.small(
                        onPressed: _scrollToTop,
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor:
                            Theme.of(context).colorScheme.onPrimary,
                        tooltip: 'Scroll to top',
                        child: const Icon(Icons.keyboard_arrow_up),
                      )
                    : const SizedBox.shrink(),
              );
            },
          ),
        ),
      ],
    );
  }

  /// Smooth scroll to top functionality
  void _scrollToTop() {
    if (_historyScrollController.hasClients) {
      _historyScrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  /// Build action buttons row
  Widget _buildActionButtonsRow(
    BuildContext context,
    WidgetRef ref,
    HistoryState historyState,
  ) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          // Add Entry button
          Expanded(
            child: LekkyButton(
              text: '+ Add Entry',
              onPressed: () => _showAddEntryDialog(context, ref),
              type: LekkyButtonType.success,
            ),
          ),
          const SizedBox(width: 16),
          // Clear Filters button
          Expanded(
            child: LekkyButton(
              text: 'Clear Filters',
              onPressed: () => _clearFilters(ref),
              type: LekkyButtonType.secondary,
            ),
          ),
        ],
      ),
    );
  }

  /// Show dialog to add a new entry
  void _showAddEntryDialog(BuildContext context, WidgetRef ref) {
    final preferencesAsync = ref.read(preferencesProvider);

    preferencesAsync.whenData((preferences) {
      showDialog(
        context: context,
        builder: (context) => RiverpodAddEntryDialog(
          currencySymbol: preferences.currencySymbol,
          onEntryAdded: () {
            // Refresh the data when an entry is added
            ref.read(historyProvider.notifier).refresh();
          },
        ),
      );
    });
  }

  /// Show filter dialog
  void _showFilterDialog(
      BuildContext context, WidgetRef ref, HistoryState historyState) {
    showDialog(
      context: context,
      builder: (context) => HistoryFilterDialog(
        filterType: historyState.filterType,
        sortOrder: historyState.sortOrder,
        startDate: historyState.startDate,
        endDate: historyState.endDate,
      ),
    ).then((result) {
      if (result != null && result is Map<String, dynamic>) {
        // Apply all filters atomically to ensure they work together
        ref.read(historyProvider.notifier).setAllFilters(
              filterType: result['filterType'],
              sortOrder: result['sortOrder'],
              startDate: result['startDate'],
              endDate: result['endDate'],
            );
      }
    });
  }

  /// Clear all filters
  void _clearFilters(WidgetRef ref) {
    ref.read(historyProvider.notifier).clearAllFilters();
  }
}
