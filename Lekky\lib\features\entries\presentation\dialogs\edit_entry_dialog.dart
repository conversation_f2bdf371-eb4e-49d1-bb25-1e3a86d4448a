import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/providers/database_provider.dart';
import '../../../../core/providers/date_formatter_provider.dart';

import '../../../../core/shared/widgets/currency_input_field.dart';
import '../../../../core/widgets/lekky_button.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../top_ups/domain/models/top_up.dart';
import '../../domain/models/entry_state.dart';
import '../controllers/entry_controller.dart';
import '../providers/entry_provider.dart';
import '../widgets/entry_type_selector.dart';
import 'delete_confirmation_dialog.dart';

/// A dialog for editing an existing entry (meter reading or top-up)
class EditEntryDialog extends ConsumerStatefulWidget {
  /// The meter reading to edit (null if editing a top-up)
  final MeterReading? meterReading;

  /// The top-up to edit (null if editing a meter reading)
  final TopUp? topUp;

  /// Currency symbol to use
  final String currencySymbol;

  /// Callback when an entry is updated
  final VoidCallback onEntryUpdated;

  /// Callback when an entry is deleted
  final VoidCallback onEntryDeleted;

  /// Constructor
  const EditEntryDialog({
    super.key,
    this.meterReading,
    this.topUp,
    this.currencySymbol = '₦',
    required this.onEntryUpdated,
    required this.onEntryDeleted,
  });

  @override
  ConsumerState<EditEntryDialog> createState() => _EditEntryDialogState();
}

class _EditEntryDialogState extends ConsumerState<EditEntryDialog> {
  final TextEditingController _valueController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  @override
  void initState() {
    super.initState();

    // Initialize the entry provider with the existing entry
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.meterReading != null) {
        ref
            .read(entryProvider.notifier)
            .initWithMeterReading(widget.meterReading!);
      } else if (widget.topUp != null) {
        ref.read(entryProvider.notifier).initWithTopUp(widget.topUp!);
      }
      _updateTextControllers();
    });
  }

  @override
  void dispose() {
    _valueController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  /// Calculate responsive dialog width
  double _getDialogWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final calculatedWidth = screenWidth < 600
        ? screenWidth * 0.95 // Small and medium screens
        : 500.0; // Fixed width for larger screens

    // Debug: Print the calculated width
    debugPrint(
        'EditEntryDialog: screenWidth=$screenWidth, calculatedWidth=$calculatedWidth');
    return calculatedWidth;
  }

  /// Update text controllers when the entry state changes
  void _updateTextControllers() {
    final entryState = ref.read(entryProvider);
    if (_valueController.text != entryState.value.toString()) {
      _valueController.text = entryState.value.toString();
    }
    if (_notesController.text != entryState.notes) {
      _notesController.text = entryState.notes;
    }
  }

  @override
  Widget build(BuildContext context) {
    final entryState = ref.watch(entryProvider);
    final screenWidth = MediaQuery.of(context).size.width;
    final dialogWidth = _getDialogWidth(context);
    final horizontalPadding = (screenWidth - dialogWidth) / 2;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 24,
      insetPadding: EdgeInsets.symmetric(
        horizontal: horizontalPadding,
        vertical: 28, // Same height as Add Entry dialog
      ),
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              _buildDialogHeader(context),
              const SizedBox(height: 24),
              EntryTypeSelector(
                selectedType: entryState.entryType,
                onTypeChanged: (newType) {
                  ref.read(entryProvider.notifier).setEntryType(newType);
                  // Update text controllers when type changes
                  _updateTextControllers();
                },
                enabled: true, // Allow changing entry type when editing
                isDismissalMode: widget.meterReading?.isIgnored ??
                    false, // Grey buttons for dismissal entries
              ),
              const SizedBox(height: 24),
              _buildDateTimePicker(context, entryState),
              const SizedBox(height: 24),
              _buildValueInput(context, entryState),
              const SizedBox(height: 24),
              _buildNotesInput(context, entryState),
              const SizedBox(height: 24),
              _buildButtonBar(context, entryState),
            ],
          ),
        ),
      ),
    );
  }

  /// Build the dialog header
  Widget _buildDialogHeader(BuildContext context) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Icon(
          Icons.edit,
          color: theme.colorScheme.primary,
          size: 24,
        ),
        const SizedBox(width: 8),
        Text(
          'Edit Entry',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const Spacer(),
        IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.of(context).pop(),
          tooltip: 'Close',
        ),
      ],
    );
  }

  /// Build the date and time picker
  Widget _buildDateTimePicker(BuildContext context, EntryState entryState) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Date and Time',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: () => _selectDateTime(),
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              border: Border.all(
                color: entryState.validationErrors.containsKey('dateTime')
                    ? theme.colorScheme.error
                    : theme.colorScheme.outline,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  size: 18,
                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                ),
                const SizedBox(width: 8),
                Text(
                  ref
                      .watch(dateFormatterProvider)
                      .formatDateForHistory(entryState.dateTime),
                  style: TextStyle(
                    fontSize: 16,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const Spacer(),
                Icon(
                  Icons.arrow_drop_down,
                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                ),
              ],
            ),
          ),
        ),
        if (entryState.validationErrors.containsKey('dateTime'))
          Padding(
            padding: const EdgeInsets.only(top: 4, left: 8),
            child: Text(
              entryState.validationErrors['dateTime']!,
              style: TextStyle(
                fontSize: 12,
                color: theme.colorScheme.error,
              ),
            ),
          ),
      ],
    );
  }

  /// Build the value input field
  Widget _buildValueInput(BuildContext context, EntryState entryState) {
    final theme = Theme.of(context);

    // Handle special entry types differently
    if (entryState.entryType == EntryType.recordsGap ||
        entryState.entryType == EntryType.nonDuplicateEntry) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Amount',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: theme.colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: theme.colorScheme.outline.withOpacity(0.5),
                width: 1,
              ),
            ),
            child: Text(
              '--',
              style: TextStyle(
                fontSize: 16,
                color: theme.colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
          ),
        ],
      );
    }

    // Normal input for meter readings and top-ups
    final String label = entryState.entryType == EntryType.meterReading
        ? 'Meter Reading'
        : 'Top-up Amount';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        CurrencyInputField(
          value: entryState.value,
          onChanged: (value) =>
              ref.read(entryProvider.notifier).setValue(value ?? 0.0),
          currencySymbol: widget.currencySymbol,
          labelText: '',
          errorText: entryState.validationErrors['value'],
          borderRadius: BorderRadius.circular(8),
        ),
      ],
    );
  }

  /// Build the notes input field
  Widget _buildNotesInput(BuildContext context, EntryState entryState) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Notes (Optional)',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _notesController,
          maxLines: 3,
          decoration: InputDecoration(
            hintText: 'Add any additional notes here...',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          onChanged: (value) =>
              ref.read(entryProvider.notifier).setNotes(value),
        ),
      ],
    );
  }

  /// Build the button bar
  Widget _buildButtonBar(BuildContext context, EntryState entryState) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Expanded(
          child: LekkyButton(
            text: 'Delete',
            type: LekkyButtonType.destructive,
            size: LekkyButtonSize.compact,
            onPressed:
                entryState.isLoading ? null : () => _showDeleteConfirmation(),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: LekkyButton(
            text: 'Cancel',
            type: LekkyButtonType.secondary,
            size: LekkyButtonSize.compact,
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: LekkyButton(
            text: 'Save',
            type: LekkyButtonType.special,
            size: LekkyButtonSize.compact,
            isLoading: entryState.isLoading,
            onPressed: entryState.isLoading ? null : () => _updateEntry(),
          ),
        ),
      ],
    );
  }

  /// Show date and time picker
  Future<void> _selectDateTime() async {
    if (!mounted) return;

    final entryState = ref.read(entryProvider);

    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: entryState.dateTime,
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );

    if (!mounted) return;

    if (pickedDate != null) {
      final TimeOfDay? pickedTime = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(entryState.dateTime),
      );

      if (!mounted) return;

      if (pickedTime != null) {
        final newDateTime = DateTime(
          pickedDate.year,
          pickedDate.month,
          pickedDate.day,
          pickedTime.hour,
          pickedTime.minute,
        );

        ref.read(entryProvider.notifier).setDateTime(newDateTime);
      }
    }
  }

  /// Update the entry
  Future<void> _updateEntry() async {
    final entryState = ref.read(entryProvider);
    bool success;

    // Check if entry type has changed and handle conversion
    if (entryState.hasTypeChanged) {
      if (widget.meterReading != null &&
          entryState.entryType == EntryType.topUp) {
        // Convert meter reading to top-up
        success = await ref
            .read(entryProvider.notifier)
            .convertMeterReadingToTopUp(widget.meterReading!.id!);
      } else if (widget.topUp != null &&
          entryState.entryType == EntryType.meterReading) {
        // Convert top-up to meter reading
        success = await ref
            .read(entryProvider.notifier)
            .convertTopUpToMeterReading(widget.topUp!.id!);
      } else {
        success = false;
      }
    } else {
      // Standard update without type conversion
      if (widget.meterReading != null) {
        success = await ref
            .read(entryProvider.notifier)
            .updateMeterReading(widget.meterReading!.id!);
      } else if (widget.topUp != null) {
        success = await ref
            .read(entryProvider.notifier)
            .updateTopUp(widget.topUp!.id!);
      } else {
        success = false;
      }
    }

    if (!mounted) return;

    if (success) {
      widget.onEntryUpdated();
      context.pop();
    } else {
      final entryState = ref.read(entryProvider);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(entryState.errorMessage ?? 'Failed to update entry'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Show delete confirmation dialog
  Future<void> _showDeleteConfirmation() async {
    if (!mounted) return;

    final entryState = ref.read(entryProvider);

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => DeleteConfirmationDialog(
        entryType: entryState.entryType,
        value: entryState.value,
        date: entryState.dateTime,
        currencySymbol: widget.currencySymbol,
      ),
    );

    if (!mounted) return;

    if (confirmed == true) {
      try {
        if (widget.meterReading != null) {
          // Delete meter reading using repository
          final meterReadingRepo = ref.read(meterReadingRepositoryProvider);
          await meterReadingRepo.deleteMeterReading(widget.meterReading!.id!);
        } else if (widget.topUp != null) {
          // Delete top-up using repository
          final topUpRepo = ref.read(topUpRepositoryProvider);
          await topUpRepo.deleteTopUp(widget.topUp!.id!);
        }

        if (!mounted) return;

        widget.onEntryDeleted();
        context.pop();
      } catch (e) {
        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete entry: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
