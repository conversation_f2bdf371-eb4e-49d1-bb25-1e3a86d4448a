import 'package:flutter/material.dart';

import '../../../core/utils/logger.dart';

/// Logger instance for PayPal service
final _logger = Logger('PayPal');

/// Service for handling PayPal donations
class PayPalService {
  /// PayPal Client ID
  static const String clientId =
      'AeJ8SgEUo-UKBlhW3GS_BTD0uMz8jzFnc08c5N9SfBA344bYfu61uvpDdmP4_O3hRAKql7e-Lr8hrKW-';

  /// Display App Name
  static const String appName = 'Lekky';

  /// Whether to use sandbox mode
  static const bool sandboxMode = true;

  /// Return URL - TODO: Configure proper app deep link
  static const String returnURL = 'lekky://payment/success';

  /// Cancel URL - TODO: Configure proper app deep link
  static const String cancelURL = 'lekky://payment/cancel';

  /// Donation description
  static const String description = 'Support Lekky App';

  /// Available donation amounts
  static const List<String> donationAmounts = [
    '1.00',
    '2.00',
    '5.00',
    '10.00',
    '20.00',
    '50.00'
  ];

  /// Process a donation
  static Future<bool> processDonation(
      BuildContext context, String amount, String currency) async {
    try {
      // Input validation
      if (!_isValidAmount(amount)) {
        _showErrorDialog(
            context, 'Invalid donation amount. Please select a valid amount.');
        return false;
      }

      if (!_isValidCurrency(currency)) {
        _showErrorDialog(context, 'Invalid currency. Please try again.');
        return false;
      }

      _logger.i('PayPalService: Processing donation of $amount $currency');

      // TODO: Implement secure server-side PayPal integration
      _logger.w(
          'PayPalService: PayPal integration is disabled due to security concerns. Implement server-side integration.');
      _showErrorDialog(context,
          'PayPal integration is temporarily disabled. Please try again later.');

      return true;
    } catch (e) {
      _logger.e('PayPalService: Error processing donation',
          details: e.toString());
      // Check if the context is still valid
      if (context.mounted) {
        _showErrorDialog(context, e.toString());
      }
      return false;
    }
  }

  /// Show error dialog
  static void _showErrorDialog(BuildContext context, String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Donation Error'),
        content: Text('There was an error processing your donation: $error'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  /// Validate donation amount
  static bool _isValidAmount(String amount) {
    if (amount.isEmpty) return false;

    final double? parsedAmount = double.tryParse(amount);
    if (parsedAmount == null) return false;

    // Check if amount is in allowed list
    return donationAmounts.contains(amount) &&
        parsedAmount > 0 &&
        parsedAmount <= 1000;
  }

  /// Validate currency code
  static bool _isValidCurrency(String currency) {
    if (currency.isEmpty) return false;

    // Allow common currencies
    const allowedCurrencies = ['USD', 'EUR', 'GBP', 'CAD', 'AUD'];
    return allowedCurrencies.contains(currency.toUpperCase());
  }
}
