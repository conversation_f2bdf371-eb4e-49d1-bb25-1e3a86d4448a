// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lekky/main.dart';

void main() {
  testWidgets('App initializes without crashing', (WidgetTester tester) async {
    // Build our app with ProviderScope and trigger a frame.
    await tester.pumpWidget(
      const ProviderScope(
        child: LekkyApp(),
      ),
    );

    // Wait for initial frame
    await tester.pump();

    // Wait for splash screen delay (2 seconds) plus some buffer
    await tester.pump(const Duration(seconds: 2));
    await tester.pump();

    // Verify that the app has rendered some content (any MaterialApp content)
    expect(find.byType(MaterialApp), findsOneWidget);

    // The app should have navigated to some screen without crashing
    // We don't test specific text since the navigation depends on app state
  });
}
