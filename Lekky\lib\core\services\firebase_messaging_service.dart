import 'dart:io';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';
import '../../features/notifications/domain/models/notification.dart';
import '../../features/notifications/data/notification_service.dart';
import '../di/service_locator.dart';

/// Service for managing Firebase Cloud Messaging
class FirebaseMessagingService {
  static final FirebaseMessagingService _instance = FirebaseMessagingService._internal();
  factory FirebaseMessagingService() => _instance;
  FirebaseMessagingService._internal();

  FirebaseMessaging? _messaging;
  bool _isInitialized = false;

  /// Initialize Firebase Cloud Messaging
  Future<bool> initialize() async {
    try {
      Logger.info('FirebaseMessagingService: Initializing');

      // Initialize Firebase Core
      await Firebase.initializeApp();
      
      _messaging = FirebaseMessaging.instance;
      
      // Request permission for notifications
      final settings = await _messaging!.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
        criticalAlert: Platform.isIOS,
      );

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        Logger.info('FirebaseMessagingService: Permission granted');
        
        // Get FCM token
        final token = await _messaging!.getToken();
        if (token != null) {
          await _storeToken(token);
          Logger.info('FirebaseMessagingService: Token obtained and stored');
        }

        // Set up message handlers
        await _setupMessageHandlers();
        
        _isInitialized = true;
        Logger.info('FirebaseMessagingService: Initialization complete');
        return true;
      } else {
        Logger.warning('FirebaseMessagingService: Permission denied');
        return false;
      }
    } catch (e) {
      Logger.error('FirebaseMessagingService: Initialization failed: $e');
      return false;
    }
  }

  /// Set up message handlers for foreground and background
  Future<void> _setupMessageHandlers() async {
    if (_messaging == null) return;

    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle background message taps
    FirebaseMessaging.onMessageOpenedApp.listen(_handleBackgroundMessageTap);

    // Handle app launch from terminated state
    final initialMessage = await _messaging!.getInitialMessage();
    if (initialMessage != null) {
      _handleBackgroundMessageTap(initialMessage);
    }

    // Set up background message handler
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  }

  /// Handle foreground messages
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    try {
      Logger.info('FirebaseMessagingService: Foreground message received: ${message.messageId}');
      
      final notification = _parseRemoteMessage(message);
      if (notification != null) {
        final notificationService = await serviceLocator.getAsync<NotificationService>();
        await notificationService.showNotification(notification);
      }
    } catch (e) {
      Logger.error('FirebaseMessagingService: Error handling foreground message: $e');
    }
  }

  /// Handle background message taps
  Future<void> _handleBackgroundMessageTap(RemoteMessage message) async {
    try {
      Logger.info('FirebaseMessagingService: Background message tap: ${message.messageId}');
      
      // Store message for app to handle when ready
      await _storeMessageForHandling(message);
    } catch (e) {
      Logger.error('FirebaseMessagingService: Error handling background message tap: $e');
    }
  }

  /// Parse RemoteMessage to AppNotification
  AppNotification? _parseRemoteMessage(RemoteMessage message) {
    try {
      final data = message.data;
      final notification = message.notification;
      
      if (notification == null) return null;
      
      final typeString = data['type'] ?? 'welcome';
      final type = NotificationType.values.firstWhere(
        (e) => e.toString().split('.').last == typeString,
        orElse: () => NotificationType.welcome,
      );
      
      return AppNotification(
        title: notification.title ?? 'Lekky Notification',
        message: notification.body ?? '',
        type: type,
        timestamp: DateTime.now(),
      );
    } catch (e) {
      Logger.error('FirebaseMessagingService: Error parsing remote message: $e');
      return null;
    }
  }

  /// Store FCM token
  Future<void> _storeToken(String token) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('fcm_token', token);
      await prefs.setString('fcm_token_timestamp', DateTime.now().toIso8601String());
    } catch (e) {
      Logger.error('FirebaseMessagingService: Error storing token: $e');
    }
  }

  /// Store message for later handling
  Future<void> _storeMessageForHandling(RemoteMessage message) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('pending_fcm_message_id', message.messageId ?? '');
      await prefs.setString('pending_fcm_message_title', message.notification?.title ?? '');
      await prefs.setString('pending_fcm_message_body', message.notification?.body ?? '');
      await prefs.setString('pending_fcm_message_data', message.data.toString());
      await prefs.setString('pending_fcm_message_timestamp', DateTime.now().toIso8601String());
    } catch (e) {
      Logger.error('FirebaseMessagingService: Error storing message: $e');
    }
  }

  /// Get stored FCM token
  Future<String?> getToken() async {
    try {
      if (_messaging == null) return null;
      return await _messaging!.getToken();
    } catch (e) {
      Logger.error('FirebaseMessagingService: Error getting token: $e');
      return null;
    }
  }

  /// Check if FCM is available and initialized
  bool get isAvailable => _isInitialized && _messaging != null;

  /// Get FCM status for diagnostics
  Future<Map<String, dynamic>> getStatus() async {
    try {
      final status = <String, dynamic>{};
      status['isInitialized'] = _isInitialized;
      status['isAvailable'] = isAvailable;
      
      if (_messaging != null) {
        final token = await getToken();
        status['hasToken'] = token != null;
        status['tokenLength'] = token?.length ?? 0;
        
        final settings = await _messaging!.getNotificationSettings();
        status['authorizationStatus'] = settings.authorizationStatus.toString();
      }
      
      status['timestamp'] = DateTime.now().toIso8601String();
      return status;
    } catch (e) {
      Logger.error('FirebaseMessagingService: Error getting status: $e');
      return {'error': e.toString()};
    }
  }

  /// Process any pending FCM messages
  Future<void> processPendingMessages() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final messageId = prefs.getString('pending_fcm_message_id');
      
      if (messageId != null && messageId.isNotEmpty) {
        final title = prefs.getString('pending_fcm_message_title') ?? '';
        final body = prefs.getString('pending_fcm_message_body') ?? '';
        
        if (title.isNotEmpty || body.isNotEmpty) {
          final notification = AppNotification(
            title: title.isNotEmpty ? title : 'Lekky Notification',
            message: body,
            type: NotificationType.welcome,
            timestamp: DateTime.now(),
          );
          
          final notificationService = await serviceLocator.getAsync<NotificationService>();
          await notificationService.showNotification(notification);
          
          // Clear pending message
          await prefs.remove('pending_fcm_message_id');
          await prefs.remove('pending_fcm_message_title');
          await prefs.remove('pending_fcm_message_body');
          await prefs.remove('pending_fcm_message_data');
          await prefs.remove('pending_fcm_message_timestamp');
          
          Logger.info('FirebaseMessagingService: Processed pending FCM message');
        }
      }
    } catch (e) {
      Logger.error('FirebaseMessagingService: Error processing pending messages: $e');
    }
  }

  /// Subscribe to topic for broadcast notifications
  Future<bool> subscribeToTopic(String topic) async {
    try {
      if (_messaging == null) return false;
      
      await _messaging!.subscribeToTopic(topic);
      Logger.info('FirebaseMessagingService: Subscribed to topic: $topic');
      return true;
    } catch (e) {
      Logger.error('FirebaseMessagingService: Error subscribing to topic: $e');
      return false;
    }
  }

  /// Unsubscribe from topic
  Future<bool> unsubscribeFromTopic(String topic) async {
    try {
      if (_messaging == null) return false;
      
      await _messaging!.unsubscribeFromTopic(topic);
      Logger.info('FirebaseMessagingService: Unsubscribed from topic: $topic');
      return true;
    } catch (e) {
      Logger.error('FirebaseMessagingService: Error unsubscribing from topic: $e');
      return false;
    }
  }
}

/// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  try {
    await Firebase.initializeApp();
    Logger.info('FirebaseMessagingService: Background message received: ${message.messageId}');
    
    // Store message for foreground processing
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('background_fcm_message_id', message.messageId ?? '');
    await prefs.setString('background_fcm_message_title', message.notification?.title ?? '');
    await prefs.setString('background_fcm_message_body', message.notification?.body ?? '');
    await prefs.setString('background_fcm_message_timestamp', DateTime.now().toIso8601String());
  } catch (e) {
    Logger.error('FirebaseMessagingService: Background handler error: $e');
  }
}
