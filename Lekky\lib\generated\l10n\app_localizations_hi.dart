import 'app_localizations.dart';

/// The translations for Hindi (`hi`).
class AppLocalizationsHi extends AppLocalizations {
  AppLocalizationsHi([String locale = 'hi']) : super(locale);

  @override
  String get appName => 'Lekky';

  @override
  String get tagline => 'आपका प्रीपेड मीटर सहायक';

  @override
  String get splashQuote => 'मैं कंजूस नहीं हूं—मैं किलोवाट-सचेत हूं।';

  @override
  String get checkingPermissions => 'अनुमतियां जांची जा रही हैं...';

  @override
  String get initializing => 'प्रारंभ हो रहा है...';

  @override
  String get welcomeTitle => 'Lekky में आपका स्वागत है';

  @override
  String get welcomeSubtitle => 'आपका व्यक्तिगत प्रीपेड मीटर सहायक';

  @override
  String get trackUsage => 'अपना उपयोग ट्रैक करें';

  @override
  String get getAlerts => 'समय पर अलर्ट प्राप्त करें';

  @override
  String get viewHistory => 'इतिहास देखें';

  @override
  String get calculateCosts => 'लागत की गणना करें';

  @override
  String get trackUsageDesc => 'अपनी बिजली की खपत और खर्च की निगरानी करें';

  @override
  String get getAlertsDesc => 'जब आपका बैलेंस कम हो तो सूचनाएं प्राप्त करें';

  @override
  String get viewHistoryDesc => 'अपने पिछले मीटर रीडिंग और टॉप-अप देखें';

  @override
  String get calculateCostsDesc => 'विभिन्न अवधियों में अपनी बिजली की लागत का अनुमान लगाएं';

  @override
  String get getStarted => 'शुरू करें';

  @override
  String get restoreData => 'पिछला डेटा पुनर्स्थापित करें';

  @override
  String get restoreHelper => 'क्या आपके पास किसी अन्य डिवाइस से बैकअप है?';

  @override
  String get restoreDataTitle => 'डेटा पुनर्स्थापित करें';

  @override
  String get restoreDataContent => 'यह सुविधा आपको बैकअप फ़ाइल से डेटा पुनर्स्थापित करने की अनुमति देगी।';

  @override
  String get cancel => 'रद्द करें';

  @override
  String get chooseFile => 'फ़ाइल चुनें';

  @override
  String get regionSettings => 'क्षेत्रीय सेटिंग्स';

  @override
  String get language => 'भाषा';

  @override
  String get currency => 'मुद्रा';

  @override
  String get selectLanguage => 'ऐप इंटरफेस के लिए अपनी पसंदीदा भाषा चुनें।';

  @override
  String get selectCurrency => 'अपने मीटर रीडिंग के लिए मुद्रा चुनें।';

  @override
  String get currencyTip => 'सुझाव: वह मुद्रा चुनें जो आपके बिजली के बिलों से मेल खाती हो।';

  @override
  String get perDay => '/दिन';

  @override
  String get dashboard => 'डैशबोर्ड';

  @override
  String get history => 'इतिहास';

  @override
  String get settings => 'सेटिंग्स';

  @override
  String get noEntriesFound => 'कोई प्रविष्टि नहीं मिली';

  @override
  String get tryAdjustingFilters => 'अधिक प्रविष्टियां देखने के लिए अपने फ़िल्टर समायोजित करने का प्रयास करें';

  @override
  String get noEntriesYet => 'अभी तक कोई प्रविष्टि नहीं';

  @override
  String get addFirstEntry => 'शुरू करने के लिए अपनी पहली मीटर रीडिंग या टॉप-अप जोड़ें';

  @override
  String get errorLoadingData => 'डेटा लोड करने में त्रुटि';

  @override
  String errorLoadingPreferences(String error) {
    return 'प्राथमिकताएं लोड करने में त्रुटि: $error';
  }

  @override
  String get meterReading => 'मीटर रीडिंग';

  @override
  String get topUp => 'टॉप अप';

  @override
  String get lastUpdated => 'अंतिम अपडेट';

  @override
  String get daysRemaining => 'शेष दिन';

  @override
  String get currentBalance => 'वर्तमान बैलेंस';

  @override
  String get usageStatistics => 'उपयोग आंकड़े';

  @override
  String get recentAverage => 'हाल का औसत';

  @override
  String get totalAverage => 'कुल औसत';

  @override
  String get dailyUsage => 'दैनिक उपयोग';

  @override
  String get topUpStatistics => 'टॉप अप आंकड़े';

  @override
  String get daysToAlert => 'अलर्ट तक दिन';

  @override
  String get daysToZero => 'शून्य तक दिन';

  @override
  String get quickActions => 'त्वरित क्रियाएं';

  @override
  String get addEntry => 'प्रविष्टि जोड़ें';

  @override
  String get recentActivity => 'हाल की गतिविधि';

  @override
  String get viewAll => 'सभी देखें';

  @override
  String get save => 'सहेजें';

  @override
  String get delete => 'हटाएं';

  @override
  String get edit => 'संपादित करें';

  @override
  String get add => 'जोड़ें';

  @override
  String get close => 'बंद करें';

  @override
  String get ok => 'ठीक है';

  @override
  String get yes => 'हां';

  @override
  String get no => 'नहीं';

  @override
  String get loading => 'लोड हो रहा है...';

  @override
  String get saving => 'सहेजा जा रहा है...';

  @override
  String get region => 'क्षेत्र';

  @override
  String get languageCurrency => 'भाषा, मुद्रा';

  @override
  String get recentAvgUsage => 'हाल का औसत लगातार रीडिंग के बीच उपयोग दिखाता है';

  @override
  String get tapNotificationBell => 'सभी सूचनाएं देखने के लिए सूचना घंटी आइकन पर टैप करें';

  @override
  String get addReadingsRegularly => 'बेहतर उपयोग आंकड़ों के लिए नियमित रूप से नई मीटर रीडिंग जोड़ें';

  @override
  String get setupAlertsLowBalance => 'कम बैलेंस होने पर सूचना प्राप्त करने के लिए अलर्ट सेट करें';

  @override
  String get useQuickActions => 'नई रीडिंग या टॉप-अप जोड़ने के लिए त्वरित क्रियाओं का उपयोग करें';

  @override
  String get viewHistoryTip => 'सभी पिछली मीटर रीडिंग और टॉप-अप देखने के लिए अपना इतिहास देखें';

  @override
  String get notificationsGrouped => 'आसान संगठन के लिए सूचनाएं प्रकार के अनुसार समूहीकृत हैं';

  @override
  String get swipeNotifications => 'सूचनाओं को पढ़ा गया चिह्नित करने के लिए बाएं स्वाइप करें, हटाने के लिए दाएं स्वाइप करें';

  @override
  String get configureThresholds => 'सेटिंग्स > अलर्ट और सूचनाएं में सूचना सीमा कॉन्फ़िगर करें';

  @override
  String get lowBalanceHelp => 'कम बैलेंस अलर्ट आपको क्रेडिट खत्म होने से बचने में मदद करते हैं';

  @override
  String get daysInAdvanceTip => 'जल्दी टॉप-अप रिमाइंडर पाने के लिए \\\"दिन पहले\\\" सेट करें';

  @override
  String get today => 'आज';

  @override
  String get yesterday => 'कल';

  @override
  String get lastWeek => 'पिछला सप्ताह';

  @override
  String get lastMonth => 'पिछला महीना';

  @override
  String get never => 'कभी नहीं';

  @override
  String get days => 'दिन';

  @override
  String get day => 'दिन';

  @override
  String get hours => 'घंटे';

  @override
  String get hour => 'घंटा';

  @override
  String get minutes => 'मिनट';

  @override
  String get minute => 'मिनट';

  @override
  String get retry => 'पुनः प्रयास';

  @override
  String get skip => 'छोड़ें';

  @override
  String get complete => 'पूर्ण';

  @override
  String get failed => 'असफल';

  @override
  String get syncing => 'सिंक हो रहा है...';

  @override
  String get deleting => 'हटाया जा रहा है...';

  @override
  String get noMeterReading => 'कोई मीटर रीडिंग उपलब्ध नहीं';

  @override
  String get addFirstReading => 'अपनी पहली रीडिंग जोड़ें';

  @override
  String get nextTopUp => 'अगला टॉप-अप';

  @override
  String get addReading => 'रीडिंग जोड़ें';

  @override
  String get addTopUp => 'टॉप-अप जोड़ें';

  @override
  String get noRecentActivity => 'कोई हाल की गतिविधि नहीं';

  @override
  String get invalidEntry => 'अमान्य प्रविष्टि';

  @override
  String get missingData => 'गुम डेटा';

  @override
  String get dataInconsistency => 'डेटा असंगति';

  @override
  String get validationError => 'सत्यापन त्रुटि';

  @override
  String get failedToSave => 'सहेजने में विफल';

  @override
  String get networkError => 'नेटवर्क त्रुटि';

  @override
  String get permissionDenied => 'अनुमति अस्वीकृत';

  @override
  String get fileNotFound => 'फ़ाइल नहीं मिली';

  @override
  String get invalidFileFormat => 'अमान्य फ़ाइल प्रारूप';

  @override
  String get addEntryDialog => 'प्रविष्टि जोड़ें';

  @override
  String get editEntry => 'प्रविष्टि संपादित करें';

  @override
  String get deleteEntry => 'प्रविष्टि हटाएं';

  @override
  String get confirmDelete => 'हटाने की पुष्टि करें';

  @override
  String get exportData => 'डेटा निर्यात करें';

  @override
  String get importData => 'डेटा आयात करें';

  @override
  String get settingsDialog => 'सेटिंग्स';

  @override
  String get about => 'के बारे में';

  @override
  String get lowBalanceAlert => 'कम बैलेंस अलर्ट';

  @override
  String get timeToTopUp => 'टॉप-अप का समय';

  @override
  String get meterReadingReminder => 'मीटर रीडिंग रिमाइंडर';

  @override
  String get dataBackupReminder => 'डेटा बैकअप रिमाइंडर';

  @override
  String get alertsNotifications => 'अलर्ट और सूचनाएं';

  @override
  String get dateTime => 'दिनांक और समय';

  @override
  String get theme => 'थीम';

  @override
  String get dataManagement => 'डेटा प्रबंधन';

  @override
  String get appInformation => 'ऐप जानकारी';

  @override
  String get setup => 'सेटअप';

  @override
  String get setupRegionSettings => 'क्षेत्रीय सेटिंग्स';

  @override
  String get setupRegionSettingsDesc => 'भाषा और मुद्रा प्राथमिकताएं कॉन्फ़िगर करें।';

  @override
  String get setupInitialMeterReading => 'प्रारंभिक मीटर रीडिंग';

  @override
  String get setupInitialMeterReadingDesc => 'ट्रैकिंग शुरू करने के लिए अपनी वर्तमान मीटर रीडिंग दर्ज करें।';

  @override
  String get setupAlertSettings => 'अलर्ट सेटिंग्स';

  @override
  String get setupAlertSettingsDesc => 'कॉन्फ़िगर करें कि आप अपने मीटर बैलेंस के बारे में कब अलर्ट प्राप्त करना चाहते हैं।';

  @override
  String get setupDateSettings => 'दिनांक सेटिंग्स';

  @override
  String get setupDateSettingsDesc => 'कॉन्फ़िगर करें कि ऐप में दिनांक कैसे प्रदर्शित होते हैं।';

  @override
  String get setupAppearance => 'दिखावट';

  @override
  String get setupAppearanceDesc => 'ऐप के लुक और फील को कस्टमाइज़ करें।';

  @override
  String get finishSetup => 'सेटअप पूरा करें';

  @override
  String setupFailed(String error) {
    return 'सेटअप विफल: $error';
  }

  @override
  String get pleaseCheckInputs => 'कृपया अपने इनपुट की जांच करें।';

  @override
  String get dateSettingsTitle => 'दिनांक सेटिंग्स';

  @override
  String get dateSettingsDesc => 'चुनें कि पूरे ऐप में दिनांक कैसे प्रदर्शित होंगे।';

  @override
  String get dateFormat => 'दिनांक प्रारूप';

  @override
  String get alertThreshold => 'अलर्ट थ्रेशोल्ड';

  @override
  String get alertThresholdDesc => 'जब आपका बैलेंस इस राशि से कम हो जाएगा तो आपको सूचित किया जाएगा।';

  @override
  String get daysInAdvance => 'पहले से दिन';

  @override
  String get daysInAdvanceDesc => 'क्रेडिट समाप्त होने से कितने दिन पहले रिमाइंडर भेजना है।';

  @override
  String get initialMeterReadingOptional => 'यह वैकल्पिक है। आप इस चरण को छोड़ सकते हैं और बाद में अपनी पहली मीटर रीडिंग जोड़ सकते हैं।';

  @override
  String errorLoadingSettings(String error) {
    return 'सेटिंग्स लोड करने में त्रुटि: $error';
  }
}
