import 'package:flutter/material.dart';
import '../../../../core/shared/enums/entry_enums.dart';
import '../../../../core/widgets/date_range_selector_widget.dart';
import '../../../../core/di/service_locator.dart';
import '../../../meter_readings/domain/repositories/meter_reading_repository.dart';

/// A widget that displays filter controls for the History screen
class HistoryFilterBar extends StatelessWidget {
  /// The current filter type
  final EntryFilterType filterType;

  /// The current sort order
  final EntrySortOrder sortOrder;

  /// The start date filter
  final DateTime? startDate;

  /// The end date filter
  final DateTime? endDate;

  /// Callback when the filter type changes
  final ValueChanged<EntryFilterType> onFilterTypeChanged;

  /// Callback when the sort order changes
  final ValueChanged<EntrySortOrder> onSortOrderChanged;

  /// Callback when the date range changes
  final Function(DateTime?, DateTime?) onDateRangeChanged;

  /// Constructor
  const HistoryFilterBar({
    super.key,
    required this.filterType,
    required this.sortOrder,
    required this.startDate,
    required this.endDate,
    required this.onFilterTypeChanged,
    required this.onSortOrderChanged,
    required this.onDateRangeChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: _buildFilterTypeDropdown(context),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSortOrderDropdown(context),
              ),
            ],
          ),
          const SizedBox(height: 8),
          _buildDateRangeSelector(context),
        ],
      ),
    );
  }

  /// Build the filter type dropdown
  Widget _buildFilterTypeDropdown(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Filter',
          style: TextStyle(
            fontSize: 12,
            color: theme.colorScheme.onSurface.withOpacity(0.6),
          ),
        ),
        const SizedBox(height: 4),
        Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: theme.colorScheme.outline.withOpacity(0.5),
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<EntryFilterType>(
              value: filterType,
              isExpanded: true,
              padding: const EdgeInsets.symmetric(horizontal: 12),
              borderRadius: BorderRadius.circular(8),
              items: EntryFilterType.values.map((type) {
                String label;
                switch (type) {
                  case EntryFilterType.all:
                    label = 'All Entries';
                    break;
                  case EntryFilterType.meterReadings:
                    label = 'Meter Readings';
                    break;
                  case EntryFilterType.topUps:
                    label = 'Top-ups';
                    break;
                  case EntryFilterType.invalid:
                    label = 'Invalid Entries';
                    break;
                  case EntryFilterType.dismissed:
                    label = 'Records Gap';
                    break;
                }

                // Special styling for invalid entries filter
                if (type == EntryFilterType.invalid) {
                  return DropdownMenuItem<EntryFilterType>(
                    value: type,
                    child: Row(
                      children: [
                        const Icon(
                          Icons.warning_amber,
                          color: Colors.amber,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Text(label),
                      ],
                    ),
                  );
                }

                return DropdownMenuItem<EntryFilterType>(
                  value: type,
                  child: Text(label),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  onFilterTypeChanged(value);
                }
              },
            ),
          ),
        ),
      ],
    );
  }

  /// Build the sort order dropdown
  Widget _buildSortOrderDropdown(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Sort',
          style: TextStyle(
            fontSize: 12,
            color: theme.colorScheme.onSurface.withOpacity(0.6),
          ),
        ),
        const SizedBox(height: 4),
        Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: theme.colorScheme.outline.withOpacity(0.5),
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<EntrySortOrder>(
              value: sortOrder,
              isExpanded: true,
              padding: const EdgeInsets.symmetric(horizontal: 12),
              borderRadius: BorderRadius.circular(8),
              items: EntrySortOrder.values.map((order) {
                String label;
                switch (order) {
                  case EntrySortOrder.newestFirst:
                    label = 'Newest First';
                    break;
                  case EntrySortOrder.oldestFirst:
                    label = 'Oldest First';
                    break;
                }

                return DropdownMenuItem<EntrySortOrder>(
                  value: order,
                  child: Text(label),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  onSortOrderChanged(value);
                }
              },
            ),
          ),
        ),
      ],
    );
  }

  /// Build the date range selector
  Widget _buildDateRangeSelector(BuildContext context) {
    return DateRangeSelectorWidget(
      fromDate: startDate,
      toDate: endDate,
      onFromDateChanged: (date) => onDateRangeChanged(date, endDate),
      onToDateChanged: (date) => onDateRangeChanged(startDate, date),
      onClearDateRange: () async {
        // Reset to default dates
        try {
          final meterReadingRepo = serviceLocator<MeterReadingRepository>();
          final allReadings = await meterReadingRepo.getAllMeterReadings();

          if (allReadings.isNotEmpty) {
            onDateRangeChanged(allReadings.last.date, allReadings.first.date);
          } else {
            onDateRangeChanged(null, null);
          }
        } catch (e) {
          onDateRangeChanged(null, null);
        }
      },
      getFromDateConstraints: _getFromDateConstraints,
      getToDateConstraints: _getToDateConstraints,
      themeContext: 'history',
    );
  }

  /// Get constraints for from date picker
  Future<DateConstraints> _getFromDateConstraints() async {
    try {
      final meterReadingRepo = serviceLocator<MeterReadingRepository>();
      final allReadings = await meterReadingRepo.getAllMeterReadings();

      if (allReadings.isEmpty) {
        return DateConstraints(
          firstDate: DateTime(2020),
          lastDate: DateTime.now(),
          defaultDate: DateTime.now(),
        );
      }

      // Get first meter reading date as default from date
      final firstReadingDate = allReadings.last.date;

      return DateConstraints(
        firstDate: firstReadingDate,
        lastDate: endDate ?? DateTime.now(),
        defaultDate: firstReadingDate,
      );
    } catch (e) {
      return DateConstraints(
        firstDate: DateTime(2020),
        lastDate: DateTime.now(),
        defaultDate: DateTime.now(),
      );
    }
  }

  /// Get constraints for to date picker
  Future<DateConstraints> _getToDateConstraints() async {
    try {
      final meterReadingRepo = serviceLocator<MeterReadingRepository>();
      final allReadings = await meterReadingRepo.getAllMeterReadings();

      if (allReadings.isEmpty) {
        return DateConstraints(
          firstDate: DateTime(2020),
          lastDate: DateTime.now(),
          defaultDate: DateTime.now(),
        );
      }

      // Get latest meter reading date as default to date
      final latestReadingDate = allReadings.first.date;

      return DateConstraints(
        firstDate: startDate ?? allReadings.last.date,
        lastDate: latestReadingDate,
        defaultDate: latestReadingDate,
      );
    } catch (e) {
      return DateConstraints(
        firstDate: startDate ?? DateTime(2020),
        lastDate: DateTime.now(),
        defaultDate: DateTime.now(),
      );
    }
  }
}
