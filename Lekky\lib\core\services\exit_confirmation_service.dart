// File: lib/core/services/exit_confirmation_service.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/logger.dart';

/// Service for handling app exit confirmation
class ExitConfirmationService {
  /// Show exit confirmation dialog
  static Future<bool> showExitConfirmation(BuildContext context) async {
    try {
      Logger.info('ExitConfirmationService: Showing exit confirmation dialog');
      
      final result = await showDialog<bool>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('Exit App'),
            content: const Text('Are you sure you want to exit Lekky?'),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop(false);
                },
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop(true);
                },
                child: const Text('Exit'),
              ),
            ],
          );
        },
      );
      
      final shouldExit = result ?? false;
      Logger.info('ExitConfirmationService: User chose to exit: $shouldExit');
      
      if (shouldExit) {
        // Exit the app
        SystemNavigator.pop();
      }
      
      return shouldExit;
    } catch (e, stackTrace) {
      Logger.error('ExitConfirmationService: Error showing exit confirmation: $e', stackTrace);
      return false;
    }
  }
}
