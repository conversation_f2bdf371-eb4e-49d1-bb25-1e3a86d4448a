import 'dart:io';
import 'package:flutter/services.dart';
import '../utils/logger.dart';

/// Service for managing Android foreground service
class ForegroundServiceManager {
  static final ForegroundServiceManager _instance = ForegroundServiceManager._internal();
  factory ForegroundServiceManager() => _instance;
  ForegroundServiceManager._internal();

  static const MethodChannel _channel = MethodChannel('com.lekky.app/foreground_service');
  
  bool _isServiceRunning = false;

  /// Start the foreground service
  Future<bool> startService() async {
    if (!Platform.isAndroid) {
      Logger.info('ForegroundServiceManager: Not Android, skipping service start');
      return true;
    }

    try {
      Logger.info('ForegroundServiceManager: Starting foreground service');
      
      final result = await _channel.invokeMethod('startService');
      _isServiceRunning = result == true;
      
      if (_isServiceRunning) {
        Logger.info('ForegroundServiceManager: Foreground service started successfully');
      } else {
        Logger.warning('ForegroundServiceManager: Failed to start foreground service');
      }
      
      return _isServiceRunning;
    } catch (e) {
      Logger.error('ForegroundServiceManager: Error starting service: $e');
      return false;
    }
  }

  /// Stop the foreground service
  Future<bool> stopService() async {
    if (!Platform.isAndroid) {
      Logger.info('ForegroundServiceManager: Not Android, skipping service stop');
      return true;
    }

    try {
      Logger.info('ForegroundServiceManager: Stopping foreground service');
      
      final result = await _channel.invokeMethod('stopService');
      _isServiceRunning = !(result == true);
      
      if (!_isServiceRunning) {
        Logger.info('ForegroundServiceManager: Foreground service stopped successfully');
      } else {
        Logger.warning('ForegroundServiceManager: Failed to stop foreground service');
      }
      
      return !_isServiceRunning;
    } catch (e) {
      Logger.error('ForegroundServiceManager: Error stopping service: $e');
      return false;
    }
  }

  /// Check if the foreground service is running
  Future<bool> isServiceRunning() async {
    if (!Platform.isAndroid) {
      return false;
    }

    try {
      final result = await _channel.invokeMethod('isServiceRunning');
      _isServiceRunning = result == true;
      return _isServiceRunning;
    } catch (e) {
      Logger.error('ForegroundServiceManager: Error checking service status: $e');
      return false;
    }
  }

  /// Get service status for diagnostics
  Future<Map<String, dynamic>> getServiceStatus() async {
    try {
      final status = <String, dynamic>{};
      status['platform'] = Platform.operatingSystem;
      status['isAndroid'] = Platform.isAndroid;
      
      if (Platform.isAndroid) {
        status['isRunning'] = await isServiceRunning();
        status['lastKnownState'] = _isServiceRunning;
      } else {
        status['isRunning'] = false;
        status['reason'] = 'Not Android platform';
      }
      
      status['timestamp'] = DateTime.now().toIso8601String();
      return status;
    } catch (e) {
      Logger.error('ForegroundServiceManager: Error getting service status: $e');
      return {'error': e.toString()};
    }
  }

  /// Start service based on notification settings
  Future<void> manageServiceBasedOnSettings(bool notificationsEnabled) async {
    if (!Platform.isAndroid) return;

    try {
      final isRunning = await isServiceRunning();
      
      if (notificationsEnabled && !isRunning) {
        Logger.info('ForegroundServiceManager: Starting service for enabled notifications');
        await startService();
      } else if (!notificationsEnabled && isRunning) {
        Logger.info('ForegroundServiceManager: Stopping service for disabled notifications');
        await stopService();
      }
    } catch (e) {
      Logger.error('ForegroundServiceManager: Error managing service: $e');
    }
  }

  /// Get current service state
  bool get isRunning => _isServiceRunning;
}
