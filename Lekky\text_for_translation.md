# Lekky App - Text for Translation

This document contains all text strings that need to be translated for the Le<PERSON>ky prepaid electricity meter tracking app.

## Translation Status: Tier 1 Languages (8 languages)
1. English (en) - ✅ Complete (Template/Source)
2. Spanish (es) - ✅ Complete (Professional translations)
3. Portuguese (pt) - ✅ Complete (Professional translations)
4. French (fr) - ✅ Complete (Professional translations)
5. German (de) - ✅ Complete (Professional translations)
6. Chinese (zh) - ✅ Complete (Professional translations - Simplified Chinese)
7. Hindi (hi) - ✅ Complete (Professional translations - Devanagari script)
8. Arabic (ar) - ✅ Complete (Professional translations - RTL support included)

## Implementation Status
- ✅ ARB files created for all 8 Tier 1 languages
- ✅ Flutter localization generation completed
- ✅ Professional translations integrated
- ✅ RTL support configured for Arabic
- ✅ Currency and date format considerations included
- ✅ App builds successfully with all translations

## Existing Translations (Already in ARB files)
These strings are already translated and in the ARB files:
- App name, tagline, splash quote
- Welcome screen titles and descriptions
- Basic feature descriptions
- Region settings labels
- Basic button texts (Cancel, Choose File, Get Started)

## New Strings Requiring Translation

### Dashboard Screen
- "Dashboard" (screen title)
- "Recent-avg shows usage between consecutive readings"
- "Tap the notification bell icon to view all notifications"
- "Add new meter readings regularly for better usage statistics"
- "Set up alerts to be notified when your balance is low"
- "Use the Quick Actions to add new readings or top-ups"
- "View your history to see all past meter readings and top-ups"
- "Notifications are grouped by type for easy organization"
- "Swipe left on notifications to mark as read, right to delete"
- "Configure notification thresholds in Settings > Alerts & Notifications"
- "Low balance alerts help you avoid running out of credit"
- "Set \"Days in Advance\" to get top-up reminders early"
- "Error loading preferences: {error}"

### History Screen
- "History" (screen title)
- "No entries found"
- "Try adjusting your filters to see more entries"
- "No entries yet"
- "Add your first meter reading or top-up to get started"
- "Error: {error}"

### Settings Screen
- "Settings" (screen title)
- "Region" (category title)
- "Language, Currency" (category description)
- "Language" (setting label)
- "Currency" (setting label)
- "Alerts & Notifications" (category title)
- "Date & Time" (category title)
- "Theme" (category title)
- "Data Management" (category title)
- "App Information" (category title)

### Meter Status Card
- "Meter Reading"
- "Last Updated"
- "Days Remaining"
- "Current Balance"
- "No meter reading available"
- "Add your first reading"

### Usage Statistics Card
- "Usage Statistics"
- "Recent Average"
- "Total Average"
- "Daily Usage"

### Top Up Statistics Card
- "Top Up Statistics"
- "Days to Alert"
- "Days to Zero"
- "Next Top Up"

### Quick Actions Card
- "Quick Actions"
- "Add Entry"
- "Add Reading"
- "Add Top Up"

### Recent Activity Card
- "Recent Activity"
- "View All"
- "No recent activity"

### Validation Messages
- "Invalid entry"
- "Missing data"
- "Data inconsistency"
- "Validation error"

### Error Messages
- "Error loading data"
- "Failed to save"
- "Network error"
- "Permission denied"
- "File not found"
- "Invalid file format"

### Dialog Titles
- "Add Entry"
- "Edit Entry"
- "Delete Entry"
- "Confirm Delete"
- "Export Data"
- "Import Data"
- "Settings"
- "About"

### Button Labels
- "Save"
- "Delete"
- "Edit"
- "Add"
- "Close"
- "OK"
- "Yes"
- "No"
- "Retry"
- "Skip"

### Notification Messages
- "Low balance alert"
- "Time to top up"
- "Meter reading reminder"
- "Data backup reminder"

### Date/Time Formats
- "Today"
- "Yesterday"
- "Last week"
- "Last month"
- "Never"

### Units and Measurements
- "days"
- "day"
- "hours"
- "hour"
- "minutes"
- "minute"

### Status Messages
- "Loading..."
- "Saving..."
- "Deleting..."
- "Syncing..."
- "Complete"
- "Failed"

## Translation Notes

### Context Information
- **Lekky**: App name, should remain unchanged in all languages
- **Meter Reading**: Refers to electricity meter balance/credit remaining
- **Top Up**: Adding credit/money to prepaid electricity meter
- **Alert Threshold**: The balance amount that triggers low balance warnings
- **Days in Advance**: How many days before running out of credit to send reminders

### Technical Terms
- Keep technical terms consistent across the app
- Currency symbols should adapt to local conventions
- Date formats should follow local standards
- Number formatting should follow local conventions

### Tone and Style
- Friendly and helpful tone
- Clear and concise language
- Avoid technical jargon where possible
- Use action-oriented language for buttons and calls-to-action

### Special Considerations for Arabic (RTL)
- Text direction: Right-to-Left
- Number formatting: May need special handling
- Icon positioning: May need mirroring
- Layout adjustments: Required for proper RTL display

## Placeholder Translations Needed
For initial implementation, create placeholder translations that:
1. Maintain the meaning and context
2. Use appropriate formal/informal tone for the target language
3. Consider cultural appropriateness
4. Keep technical terms consistent
5. Adapt units and formats to local conventions

## File Structure
Each language will have its own ARB file:
- `app_en.arb` (English - template)
- `app_es.arb` (Spanish - existing)
- `app_pt.arb` (Portuguese - new)
- `app_fr.arb` (French - new)
- `app_de.arb` (German - new)
- `app_zh.arb` (Chinese - new)
- `app_hi.arb` (Hindi - new)
- `app_ar.arb` (Arabic - new, RTL)
