import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/app_banner.dart';
import '../../../../core/providers/settings_provider.dart';
import '../../../../core/providers/settings_navigation_provider.dart';
import '../../../../core/models/settings_state.dart';
import '../../domain/models/settings_category.dart';
import '../../domain/models/settings_sub_branch.dart';
import '../widgets/expandable_settings_category.dart';

/// Settings screen for the app
class SettingsScreen extends ConsumerStatefulWidget {
  /// Constructor
  const SettingsScreen({super.key});

  @override
  ConsumerState<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends ConsumerState<SettingsScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // Check for route parameters to set expanded category
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _handleRouteParameters();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  /// Handle route parameters to set expanded category
  void _handleRouteParameters() {
    final currentUri = GoRouter.of(context).routeInformationProvider.value.uri;
    final expandedParam = currentUri.queryParameters['expanded'];

    if (expandedParam != null) {
      final categoryIndex = int.tryParse(expandedParam);
      if (categoryIndex != null && categoryIndex >= 0 && categoryIndex <= 7) {
        ref
            .read(settingsNavigationProvider.notifier)
            .setExpandedCategory(categoryIndex);
        _scrollToCategory(categoryIndex);
      }
    } else {
      // Clear expanded categories when no expanded parameter is present
      // This ensures clean state when navigating from other main tabs
      ref.read(settingsNavigationProvider.notifier).clearExpandedCategory();
    }
  }

  @override
  Widget build(BuildContext context) {
    final settingsAsync = ref.watch(settingsProvider);

    return Scaffold(
      body: settingsAsync.when(
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 48,
              ),
              const SizedBox(height: 16),
              const Text(
                'An error occurred while loading settings',
                style: TextStyle(color: Colors.red),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  ref.invalidate(settingsProvider);
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
        data: (settings) {
          return Column(
            children: [
              // App Banner with "Settings" title using gradient colors
              AppBanner(
                message: 'Settings',
                gradientColors: AppColors.getSettingsMainCardGradient(
                    Theme.of(context).brightness == Brightness.dark),
                textColor: AppColors.getAppBarTextColor('settings',
                    Theme.of(context).brightness == Brightness.dark),
              ),
              Expanded(
                child: ListView(
                  controller: _scrollController,
                  padding: const EdgeInsets.all(16.0),
                  children: [
                    // Region
                    _buildRegionCategory(context, settings, 0),

                    const SizedBox(height: 8),

                    // Alerts & Notifications
                    _buildNotificationsCategory(context, settings, 1),

                    const SizedBox(height: 8),

                    // Date Settings
                    _buildDateSettingsCategory(context, settings, 2),

                    const SizedBox(height: 8),

                    // Appearance
                    _buildAppearanceCategory(context, settings, 3),

                    const SizedBox(height: 8),

                    // Data Backup
                    _buildDataBackupCategory(context, settings, 4),

                    const SizedBox(height: 8),

                    // About
                    _buildAboutCategory(context, settings, 5),

                    const SizedBox(height: 8),

                    // Donate
                    _buildDonateCategory(context, settings, 6),

                    const SizedBox(height: 8),

                    // Testing
                    _buildTestingCategory(context, settings, 7),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  /// Scroll to bring the expanded category into view
  void _scrollToCategory(int categoryIndex) {
    if (_scrollController.hasClients) {
      // Calculate approximate position for the category
      // Each category card is roughly 80-100 pixels tall when collapsed
      final double targetPosition = categoryIndex * 90.0;

      _scrollController.animateTo(
        targetPosition,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  // Helper methods to build each category

  Widget _buildRegionCategory(
      BuildContext context, SettingsState settings, int index) {
    final navigationState = ref.watch(settingsNavigationProvider);
    final expandedCategoryIndex = navigationState.expandedCategoryIndex;

    final category = SettingsCategory(
      title: 'Region',
      icon: Icons.public,
      iconColor: Colors.blue,
      isEnabled: expandedCategoryIndex == index,
      onToggle: (enabled) {
        ref.read(settingsNavigationProvider.notifier).toggleCategory(index);
        if (enabled) {
          _scrollToCategory(index);
        }
      },
      currentValue: "Language, Currency",
      subBranches: [
        SettingsSubBranch(
          title: 'Language',
          icon: Icons.language,
          iconColor: Colors.blue,
          routePath: '/main-settings/language',
          currentValue: settings.language,
        ),
        SettingsSubBranch(
          title: 'Currency',
          icon: Icons.currency_exchange,
          iconColor: Colors.blue,
          routePath: '/main-settings/currency',
          currentValue: settings.currency,
        ),
      ],
    );

    return ExpandableSettingsCategory(
      category: category,
      isExpanded: expandedCategoryIndex == index,
      onExpansionChanged: (expanded) {
        ref.read(settingsNavigationProvider.notifier).toggleCategory(index);
      },
    );
  }

  Widget _buildNotificationsCategory(
      BuildContext context, SettingsState settings, int index) {
    final navigationState = ref.watch(settingsNavigationProvider);
    final expandedCategoryIndex = navigationState.expandedCategoryIndex;

    final category = SettingsCategory(
      title: 'Alerts & Notifications',
      icon: Icons.notifications,
      iconColor: Colors.blue,
      isEnabled: expandedCategoryIndex == index,
      onToggle: (enabled) {
        ref.read(settingsNavigationProvider.notifier).toggleCategory(index);
        if (enabled) {
          _scrollToCategory(index);
        }
      },
      currentValue: 'Alerts, Thresholds, Reminders',
      subBranches: [
        SettingsSubBranch(
          title: 'Alert Threshold',
          icon: Icons.warning,
          iconColor: Colors.orange,
          routePath: '/main-settings/alert-threshold',
          currentValue: settings.formattedAlertThreshold,
        ),
        SettingsSubBranch(
          title: 'Days in Advance',
          icon: Icons.calendar_today,
          iconColor: Colors.orange,
          routePath: '/main-settings/days-advance',
          currentValue: settings.formattedDaysInAdvance,
        ),
        const SettingsSubBranch(
          title: 'Notification Types',
          icon: Icons.notifications_active,
          iconColor: Colors.blue,
          routePath: '/main-settings/notification-types',
        ),
        const SettingsSubBranch(
          title: 'Reminders',
          icon: Icons.alarm,
          iconColor: Colors.blue,
          routePath: '/main-settings/reminders',
        ),
        const SettingsSubBranch(
          title: 'Notification Utilities',
          icon: Icons.troubleshoot,
          iconColor: Colors.purple,
          routePath: '/main-settings/notification-utilities',
        ),
      ],
    );

    return ExpandableSettingsCategory(
      category: category,
      isExpanded: expandedCategoryIndex == index,
      onExpansionChanged: (expanded) {
        ref.read(settingsNavigationProvider.notifier).toggleCategory(index);
      },
    );
  }

  Widget _buildDateSettingsCategory(
      BuildContext context, SettingsState settings, int index) {
    final navigationState = ref.watch(settingsNavigationProvider);
    final expandedCategoryIndex = navigationState.expandedCategoryIndex;

    final category = SettingsCategory(
      title: 'Date Settings',
      icon: Icons.calendar_today,
      iconColor: Colors.blue,
      isEnabled: expandedCategoryIndex == index,
      onToggle: (enabled) {
        ref.read(settingsNavigationProvider.notifier).toggleCategory(index);
        if (enabled) {
          _scrollToCategory(index);
        }
      },
      currentValue: 'Format, Date & Time',
      subBranches: [
        SettingsSubBranch(
          title: 'Date Format',
          icon: Icons.date_range,
          iconColor: Colors.blue,
          routePath: '/main-settings/date-format',
          currentValue: settings.dateFormat,
        ),
        SettingsSubBranch(
          title: 'Time Display',
          icon: Icons.access_time,
          iconColor: Colors.blue,
          routePath: '/main-settings/time-display',
          currentValue: settings.showTimeWithDate ? 'Shown' : 'Hidden',
        ),
      ],
    );

    return ExpandableSettingsCategory(
      category: category,
      isExpanded: expandedCategoryIndex == index,
      onExpansionChanged: (expanded) {
        ref.read(settingsNavigationProvider.notifier).toggleCategory(index);
      },
    );
  }

  Widget _buildAppearanceCategory(
      BuildContext context, SettingsState settings, int index) {
    final navigationState = ref.watch(settingsNavigationProvider);
    final expandedCategoryIndex = navigationState.expandedCategoryIndex;

    final category = SettingsCategory(
      title: 'Appearance',
      icon: Icons.palette,
      iconColor: Colors.blue,
      isEnabled: expandedCategoryIndex == index,
      onToggle: (enabled) {
        ref.read(settingsNavigationProvider.notifier).toggleCategory(index);
        if (enabled) {
          _scrollToCategory(index);
        }
      },
      currentValue: settings.themeModeDisplayName,
      subBranches: [
        SettingsSubBranch(
          title: 'Theme Options',
          icon: Icons.brightness_6,
          iconColor: Colors.blue,
          routePath: '/main-settings/theme',
          currentValue: settings.themeModeDisplayName,
        ),
      ],
    );

    return ExpandableSettingsCategory(
      category: category,
      isExpanded: expandedCategoryIndex == index,
      onExpansionChanged: (expanded) {
        ref.read(settingsNavigationProvider.notifier).toggleCategory(index);
      },
    );
  }

  Widget _buildDataBackupCategory(
      BuildContext context, SettingsState settings, int index) {
    final navigationState = ref.watch(settingsNavigationProvider);
    final expandedCategoryIndex = navigationState.expandedCategoryIndex;

    final category = SettingsCategory(
      title: 'Data Management',
      icon: Icons.backup,
      iconColor: Colors.blue,
      isEnabled: expandedCategoryIndex == index,
      onToggle: (enabled) {
        ref.read(settingsNavigationProvider.notifier).toggleCategory(index);
        if (enabled) {
          _scrollToCategory(index);
        }
      },
      currentValue: 'Backup, Restore, Delete All',
      subBranches: [
        const SettingsSubBranch(
          title: 'Export Data',
          icon: Icons.file_download,
          iconColor: Colors.blue,
          routePath: '/main-settings/csv/export',
        ),
        const SettingsSubBranch(
          title: 'Import Data',
          icon: Icons.file_upload,
          iconColor: Colors.orange,
          routePath: '/main-settings/csv/import',
        ),
        const SettingsSubBranch(
          title: 'Delete All Data',
          icon: Icons.delete_forever,
          iconColor: Colors.red,
          routePath: '/main-settings/data/delete-all',
        ),
      ],
    );

    return ExpandableSettingsCategory(
      category: category,
      isExpanded: expandedCategoryIndex == index,
      onExpansionChanged: (expanded) {
        ref.read(settingsNavigationProvider.notifier).toggleCategory(index);
      },
    );
  }

  Widget _buildAboutCategory(
      BuildContext context, SettingsState settings, int index) {
    final navigationState = ref.watch(settingsNavigationProvider);
    final expandedCategoryIndex = navigationState.expandedCategoryIndex;

    final category = SettingsCategory(
      title: 'About',
      icon: Icons.info,
      iconColor: Colors.blue,
      isEnabled: expandedCategoryIndex == index,
      onToggle: (enabled) {
        ref.read(settingsNavigationProvider.notifier).toggleCategory(index);
        if (enabled) {
          _scrollToCategory(index);
        }
      },
      currentValue: 'Info, Update, Tips & Tricks',
      subBranches: [
        const SettingsSubBranch(
          title: 'App Information',
          icon: Icons.info_outline,
          iconColor: Colors.blue,
          routePath: '/main-settings/app-information',
        ),
        const SettingsSubBranch(
          title: 'Update',
          icon: Icons.update,
          iconColor: Colors.blue,
          routePath: '/main-settings/update',
        ),
        const SettingsSubBranch(
          title: 'Tips & Tricks',
          icon: Icons.lightbulb,
          iconColor: Colors.amber,
          routePath: '/main-settings/tips-tricks',
        ),
      ],
    );

    return ExpandableSettingsCategory(
      category: category,
      isExpanded: expandedCategoryIndex == index,
      onExpansionChanged: (expanded) {
        ref.read(settingsNavigationProvider.notifier).toggleCategory(index);
      },
    );
  }

  Widget _buildDonateCategory(
      BuildContext context, SettingsState settings, int index) {
    final navigationState = ref.watch(settingsNavigationProvider);
    final expandedCategoryIndex = navigationState.expandedCategoryIndex;

    final category = SettingsCategory(
      title: 'Donate',
      icon: Icons.favorite,
      iconColor: Colors.red,
      isEnabled: expandedCategoryIndex == index,
      onToggle: (enabled) {
        ref.read(settingsNavigationProvider.notifier).toggleCategory(index);
        if (enabled) {
          _scrollToCategory(index);
        }
      },
      currentValue: 'Support Lekky',
      subBranches: [
        const SettingsSubBranch(
          title: 'Donation Options',
          icon: Icons.card_giftcard,
          iconColor: Colors.red,
          routePath: '/main-settings/donate',
        ),
      ],
    );

    return ExpandableSettingsCategory(
      category: category,
      isExpanded: expandedCategoryIndex == index,
      onExpansionChanged: (expanded) {
        ref.read(settingsNavigationProvider.notifier).toggleCategory(index);
      },
    );
  }

  Widget _buildTestingCategory(
      BuildContext context, SettingsState settings, int index) {
    final navigationState = ref.watch(settingsNavigationProvider);
    final expandedCategoryIndex = navigationState.expandedCategoryIndex;

    final category = SettingsCategory(
      title: 'Testing',
      icon: Icons.bug_report,
      iconColor: Colors.orange,
      isEnabled: expandedCategoryIndex == index,
      onToggle: (enabled) {
        ref.read(settingsNavigationProvider.notifier).toggleCategory(index);
        if (enabled) {
          _scrollToCategory(index);
        }
      },
      subBranches: [
        const SettingsSubBranch(
          title: 'Notification Debug',
          icon: Icons.notifications_active,
          iconColor: Colors.orange,
          routePath: '/debug/notifications',
        ),
      ],
    );

    return ExpandableSettingsCategory(
      category: category,
      isExpanded: expandedCategoryIndex == index,
      onExpansionChanged: (expanded) {
        ref.read(settingsNavigationProvider.notifier).toggleCategory(index);
      },
    );
  }
}
