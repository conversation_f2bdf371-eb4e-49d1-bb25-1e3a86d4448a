import 'app_localizations.dart';

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appName => 'Lekky';

  @override
  String get tagline => 'مساعد العداد المدفوع مسبقاً';

  @override
  String get splashQuote => 'لست بخيلاً—أنا واعٍ بالكيلوواط.';

  @override
  String get checkingPermissions => 'جاري فحص الأذونات...';

  @override
  String get initializing => 'جاري التهيئة...';

  @override
  String get welcomeTitle => 'مرحباً بك في Lekky';

  @override
  String get welcomeSubtitle => 'مساعدك الشخصي للعداد المدفوع مسبقاً';

  @override
  String get trackUsage => 'تتبع استهلاكك';

  @override
  String get getAlerts => 'احصل على تنبيهات في الوقت المناسب';

  @override
  String get viewHistory => 'عرض السجل';

  @override
  String get calculateCosts => 'حساب التكاليف';

  @override
  String get trackUsageDesc => 'راقب استهلاك الكهرباء وإنفاقك';

  @override
  String get getAlertsDesc => 'احصل على إشعارات عندما ينخفض رصيدك';

  @override
  String get viewHistoryDesc => 'اطلع على قراءات العداد وعمليات الشحن السابقة';

  @override
  String get calculateCostsDesc => 'قدر تكاليف الكهرباء على فترات مختلفة';

  @override
  String get getStarted => 'ابدأ';

  @override
  String get restoreData => 'استعادة البيانات السابقة';

  @override
  String get restoreHelper => 'هل لديك نسخة احتياطية من جهاز آخر؟';

  @override
  String get restoreDataTitle => 'استعادة البيانات';

  @override
  String get restoreDataContent => 'ستتيح لك هذه الميزة استعادة البيانات من ملف النسخ الاحتياطي.';

  @override
  String get cancel => 'إلغاء';

  @override
  String get chooseFile => 'اختيار ملف';

  @override
  String get regionSettings => 'الإعدادات الإقليمية';

  @override
  String get language => 'اللغة';

  @override
  String get currency => 'العملة';

  @override
  String get selectLanguage => 'اختر لغتك المفضلة لواجهة التطبيق.';

  @override
  String get selectCurrency => 'اختر العملة لقراءات العداد.';

  @override
  String get currencyTip => 'نصيحة: اختر العملة التي تتطابق مع فواتير الكهرباء.';

  @override
  String get perDay => '/يوم';

  @override
  String get dashboard => 'لوحة التحكم';

  @override
  String get history => 'السجل';

  @override
  String get settings => 'الإعدادات';

  @override
  String get noEntriesFound => 'لم يتم العثور على إدخالات';

  @override
  String get tryAdjustingFilters => 'حاول تعديل المرشحات لرؤية المزيد من الإدخالات';

  @override
  String get noEntriesYet => 'لا توجد إدخالات بعد';

  @override
  String get addFirstEntry => 'أضف أول قراءة عداد أو شحن للبدء';

  @override
  String get errorLoadingData => 'خطأ في تحميل البيانات';

  @override
  String errorLoadingPreferences(String error) {
    return 'خطأ في تحميل التفضيلات: $error';
  }

  @override
  String get meterReading => 'قراءة العداد';

  @override
  String get topUp => 'شحن';

  @override
  String get lastUpdated => 'آخر تحديث';

  @override
  String get daysRemaining => 'الأيام المتبقية';

  @override
  String get currentBalance => 'الرصيد الحالي';

  @override
  String get usageStatistics => 'إحصائيات الاستهلاك';

  @override
  String get recentAverage => 'المتوسط الحديث';

  @override
  String get totalAverage => 'المتوسط الإجمالي';

  @override
  String get dailyUsage => 'الاستهلاك اليومي';

  @override
  String get topUpStatistics => 'إحصائيات الشحن';

  @override
  String get daysToAlert => 'أيام حتى التنبيه';

  @override
  String get daysToZero => 'أيام حتى الصفر';

  @override
  String get quickActions => 'إجراءات سريعة';

  @override
  String get addEntry => 'إضافة إدخال';

  @override
  String get recentActivity => 'النشاط الحديث';

  @override
  String get viewAll => 'عرض الكل';

  @override
  String get save => 'حفظ';

  @override
  String get delete => 'حذف';

  @override
  String get edit => 'تحرير';

  @override
  String get add => 'إضافة';

  @override
  String get close => 'إغلاق';

  @override
  String get ok => 'موافق';

  @override
  String get yes => 'نعم';

  @override
  String get no => 'لا';

  @override
  String get loading => 'جاري التحميل...';

  @override
  String get saving => 'جاري الحفظ...';

  @override
  String get region => 'المنطقة';

  @override
  String get languageCurrency => 'اللغة، العملة';

  @override
  String get recentAvgUsage => 'يُظهر المتوسط الحديث الاستخدام بين القراءات المتتالية';

  @override
  String get tapNotificationBell => 'اضغط على أيقونة جرس الإشعارات لعرض جميع الإشعارات';

  @override
  String get addReadingsRegularly => 'أضف قراءات عداد جديدة بانتظام للحصول على إحصائيات استخدام أفضل';

  @override
  String get setupAlertsLowBalance => 'قم بإعداد التنبيهات ليتم إعلامك عندما يكون رصيدك منخفضًا';

  @override
  String get useQuickActions => 'استخدم الإجراءات السريعة لإضافة قراءات أو شحنات جديدة';

  @override
  String get viewHistoryTip => 'اعرض تاريخك لرؤية جميع قراءات العداد والشحنات السابقة';

  @override
  String get notificationsGrouped => 'الإشعارات مجمعة حسب النوع لسهولة التنظيم';

  @override
  String get swipeNotifications => 'اسحب يمينًا على الإشعارات لتمييزها كمقروءة، يسارًا للحذف';

  @override
  String get configureThresholds => 'قم بتكوين عتبات الإشعارات في الإعدادات > التنبيهات والإشعارات';

  @override
  String get lowBalanceHelp => 'تنبيهات الرصيد المنخفض تساعدك على تجنب نفاد الرصيد';

  @override
  String get daysInAdvanceTip => 'اضبط \\\"أيام مسبقًا\\\" للحصول على تذكيرات الشحن مبكرًا';

  @override
  String get today => 'اليوم';

  @override
  String get yesterday => 'أمس';

  @override
  String get lastWeek => 'الأسبوع الماضي';

  @override
  String get lastMonth => 'الشهر الماضي';

  @override
  String get never => 'أبدًا';

  @override
  String get days => 'أيام';

  @override
  String get day => 'يوم';

  @override
  String get hours => 'ساعات';

  @override
  String get hour => 'ساعة';

  @override
  String get minutes => 'دقائق';

  @override
  String get minute => 'دقيقة';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get skip => 'تخطي';

  @override
  String get complete => 'مكتمل';

  @override
  String get failed => 'فشل';

  @override
  String get syncing => 'جاري المزامنة...';

  @override
  String get deleting => 'جاري الحذف...';

  @override
  String get noMeterReading => 'لا توجد قراءة عداد متاحة';

  @override
  String get addFirstReading => 'أضف قراءتك الأولى';

  @override
  String get nextTopUp => 'التعبئة التالية';

  @override
  String get addReading => 'إضافة قراءة';

  @override
  String get addTopUp => 'إضافة تعبئة';

  @override
  String get noRecentActivity => 'لا يوجد نشاط أخير';

  @override
  String get invalidEntry => 'إدخال غير صالح';

  @override
  String get missingData => 'بيانات مفقودة';

  @override
  String get dataInconsistency => 'عدم تناسق البيانات';

  @override
  String get validationError => 'خطأ التحقق';

  @override
  String get failedToSave => 'فشل في الحفظ';

  @override
  String get networkError => 'خطأ في الشبكة';

  @override
  String get permissionDenied => 'تم رفض الإذن';

  @override
  String get fileNotFound => 'الملف غير موجود';

  @override
  String get invalidFileFormat => 'تنسيق الملف غير صالح';

  @override
  String get addEntryDialog => 'إضافة إدخال';

  @override
  String get editEntry => 'تعديل إدخال';

  @override
  String get deleteEntry => 'حذف إدخال';

  @override
  String get confirmDelete => 'تأكيد الحذف';

  @override
  String get exportData => 'تصدير البيانات';

  @override
  String get importData => 'استيراد البيانات';

  @override
  String get settingsDialog => 'الإعدادات';

  @override
  String get about => 'حول';

  @override
  String get lowBalanceAlert => 'تنبيه الرصيد المنخفض';

  @override
  String get timeToTopUp => 'حان وقت التعبئة';

  @override
  String get meterReadingReminder => 'تذكير قراءة العداد';

  @override
  String get dataBackupReminder => 'تذكير نسخ البيانات احتياطيًا';

  @override
  String get alertsNotifications => 'التنبيهات والإشعارات';

  @override
  String get dateTime => 'التاريخ والوقت';

  @override
  String get theme => 'الثيم';

  @override
  String get dataManagement => 'إدارة البيانات';

  @override
  String get appInformation => 'معلومات التطبيق';

  @override
  String get setup => 'الإعداد';

  @override
  String get setupRegionSettings => 'إعدادات المنطقة';

  @override
  String get setupRegionSettingsDesc => 'تكوين تفضيلات اللغة والعملة.';

  @override
  String get setupInitialMeterReading => 'قراءة العداد الأولية';

  @override
  String get setupInitialMeterReadingDesc => 'أدخل قراءة العداد الحالية لبدء التتبع.';

  @override
  String get setupAlertSettings => 'إعدادات التنبيه';

  @override
  String get setupAlertSettingsDesc => 'تكوين متى تريد تلقي تنبيهات حول رصيد العداد.';

  @override
  String get setupDateSettings => 'إعدادات التاريخ';

  @override
  String get setupDateSettingsDesc => 'تكوين كيفية عرض التواريخ في التطبيق.';

  @override
  String get setupAppearance => 'المظهر';

  @override
  String get setupAppearanceDesc => 'تخصيص مظهر التطبيق.';

  @override
  String get finishSetup => 'إنهاء الإعداد';

  @override
  String setupFailed(String error) {
    return 'فشل الإعداد: $error';
  }

  @override
  String get pleaseCheckInputs => 'يرجى التحقق من مدخلاتك.';

  @override
  String get dateSettingsTitle => 'إعدادات التاريخ';

  @override
  String get dateSettingsDesc => 'اختر كيفية عرض التواريخ في جميع أنحاء التطبيق.';

  @override
  String get dateFormat => 'تنسيق التاريخ';

  @override
  String get alertThreshold => 'عتبة التنبيه';

  @override
  String get alertThresholdDesc => 'سيتم إشعارك عندما ينخفض رصيدك عن هذا المبلغ.';

  @override
  String get daysInAdvance => 'أيام مسبقة';

  @override
  String get daysInAdvanceDesc => 'كم يوماً قبل نفاد الرصيد لإرسال التذكيرات.';

  @override
  String get initialMeterReadingOptional => 'هذا اختياري. يمكنك تخطي هذه الخطوة وإضافة قراءة العداد الأولى لاحقاً.';

  @override
  String errorLoadingSettings(String error) {
    return 'خطأ في تحميل الإعدادات: $error';
  }
}
