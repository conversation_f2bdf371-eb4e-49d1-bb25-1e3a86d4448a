import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';
import '../utils/reminder_calculator.dart';
import '../constants/preference_keys.dart';
import '../di/service_locator.dart';
import 'reminder_content_generator.dart';
import '../../features/notifications/data/notification_service.dart';
import '../../features/notifications/domain/models/notification.dart';
import '../../features/notifications/domain/repositories/notification_repository.dart';
import '../../features/meter_readings/domain/repositories/meter_reading_repository.dart';

/// Service for managing automatic reminder scheduling
class ReminderSchedulingService {
  static final ReminderSchedulingService _instance =
      ReminderSchedulingService._internal();

  factory ReminderSchedulingService() => _instance;
  ReminderSchedulingService._internal();

  static const String _scheduledRemindersKey = 'scheduled_reminder_ids';
  static const int _maxScheduledReminders = 1;

  /// Schedule reminders based on current settings with simplified logic
  Future<void> scheduleReminders() async {
    try {
      final settings = await _loadReminderSettings();

      if (!settings['enabled'] || settings['startDateTime'] == null) {
        await cancelAllReminders();
        return;
      }

      // Cancel existing reminders first
      await cancelAllReminders();

      final notificationService =
          await serviceLocator.getAsync<NotificationService>();
      final startDateTime = settings['startDateTime'] as DateTime;
      final frequency = settings['frequency'] as String;

      // Validate settings before scheduling
      if (!_validateReminderSettings(startDateTime, frequency)) {
        Logger.error(
            'Invalid reminder settings: startDateTime=$startDateTime, frequency=$frequency');
        return;
      }

      // Create notification with existing messages
      final notification = AppNotification(
        title: 'Meter Reading Reminder',
        message: 'It\'s time to take a new meter reading.',
        timestamp: startDateTime,
        type: NotificationType.readingReminder,
      );

      // Schedule based on frequency using native OS repetition
      await _scheduleReminderByFrequency(
        notificationService,
        notification,
        startDateTime,
        frequency,
      );

      // Verify scheduling was successful
      await _verifySchedulingSuccess(frequency, startDateTime);

      Logger.info(
          'Successfully scheduled $frequency reminder starting at ${startDateTime.toIso8601String()}');
    } catch (error, stackTrace) {
      Logger.error('Failed to schedule reminders: $error', stackTrace);
      // Attempt fallback scheduling
      await _attemptFallbackScheduling();
    }
  }

  /// Schedule reminder based on frequency using native OS repetition
  Future<void> _scheduleReminderByFrequency(
    NotificationService notificationService,
    AppNotification notification,
    DateTime startDateTime,
    String frequency,
  ) async {
    final now = DateTime.now();
    DateTime nextReminderTime;

    // Calculate next occurrence based on frequency
    switch (frequency) {
      case 'daily':
        nextReminderTime = _getNextDailyReminder(startDateTime, now);
        break;
      case 'weekly':
        nextReminderTime = _getNextWeeklyReminder(startDateTime, now);
        break;
      case 'bi-weekly':
        nextReminderTime = _getNextBiWeeklyReminder(startDateTime, now);
        break;
      case 'monthly':
        nextReminderTime = _getNextMonthlyReminder(startDateTime, now);
        break;
      default:
        throw Exception('Unsupported frequency: $frequency');
    }

    // Schedule the repeating reminder
    if (frequency == 'bi-weekly') {
      // For bi-weekly, schedule multiple individual notifications since OS doesn't support it natively
      await _scheduleBiWeeklyReminders(
          notificationService, notification, nextReminderTime);
    } else {
      // Use native OS repetition for daily, weekly, monthly
      await notificationService.scheduleRepeatingNotification(
        notification.copyWith(timestamp: nextReminderTime),
        nextReminderTime,
        frequency,
      );
    }
  }

  /// Calculate next daily reminder time
  DateTime _getNextDailyReminder(DateTime startDateTime, DateTime now) {
    final reminderTime = DateTime(
      now.year,
      now.month,
      now.day,
      startDateTime.hour,
      startDateTime.minute,
    );

    // If time has passed today, schedule for tomorrow
    return reminderTime.isAfter(now)
        ? reminderTime
        : reminderTime.add(const Duration(days: 1));
  }

  /// Calculate next weekly reminder time
  DateTime _getNextWeeklyReminder(DateTime startDateTime, DateTime now) {
    final daysUntilTarget = (startDateTime.weekday - now.weekday) % 7;
    final targetDate = now.add(Duration(days: daysUntilTarget));
    final reminderTime = DateTime(
      targetDate.year,
      targetDate.month,
      targetDate.day,
      startDateTime.hour,
      startDateTime.minute,
    );

    // If time has passed this week, schedule for next week
    return reminderTime.isAfter(now)
        ? reminderTime
        : reminderTime.add(const Duration(days: 7));
  }

  /// Calculate next bi-weekly reminder time
  DateTime _getNextBiWeeklyReminder(DateTime startDateTime, DateTime now) {
    final daysSinceStart = now.difference(startDateTime).inDays;
    final cyclePosition = daysSinceStart % 14;
    final daysUntilNext = cyclePosition == 0 ? 0 : 14 - cyclePosition;

    final targetDate = now.add(Duration(days: daysUntilNext));
    final reminderTime = DateTime(
      targetDate.year,
      targetDate.month,
      targetDate.day,
      startDateTime.hour,
      startDateTime.minute,
    );

    return reminderTime.isAfter(now)
        ? reminderTime
        : reminderTime.add(const Duration(days: 14));
  }

  /// Calculate next monthly reminder time
  DateTime _getNextMonthlyReminder(DateTime startDateTime, DateTime now) {
    var targetMonth = now.month;
    var targetYear = now.year;

    // Try current month first
    var reminderTime = DateTime(
      targetYear,
      targetMonth,
      startDateTime.day.clamp(1, DateTime(targetYear, targetMonth + 1, 0).day),
      startDateTime.hour,
      startDateTime.minute,
    );

    // If time has passed this month, schedule for next month
    if (reminderTime.isBefore(now) || reminderTime.isAtSameMomentAs(now)) {
      targetMonth++;
      if (targetMonth > 12) {
        targetMonth = 1;
        targetYear++;
      }
      reminderTime = DateTime(
        targetYear,
        targetMonth,
        startDateTime.day
            .clamp(1, DateTime(targetYear, targetMonth + 1, 0).day),
        startDateTime.hour,
        startDateTime.minute,
      );
    }

    return reminderTime;
  }

  /// Schedule multiple bi-weekly reminders since OS doesn't support native bi-weekly repetition
  Future<void> _scheduleBiWeeklyReminders(
    NotificationService notificationService,
    AppNotification notification,
    DateTime firstReminderTime,
  ) async {
    // Schedule next 6 bi-weekly occurrences (3 months worth)
    for (int i = 0; i < 6; i++) {
      final reminderTime = firstReminderTime.add(Duration(days: i * 14));
      await notificationService.scheduleNotification(
        notification.copyWith(timestamp: reminderTime),
        reminderTime,
      );
    }
  }

  /// Validate reminder settings before scheduling
  bool _validateReminderSettings(DateTime startDateTime, String frequency) {
    // Check if start time is valid
    if (startDateTime
        .isBefore(DateTime.now().subtract(const Duration(days: 1)))) {
      return false;
    }

    // Check if frequency is supported
    const supportedFrequencies = ['daily', 'weekly', 'bi-weekly', 'monthly'];
    if (!supportedFrequencies.contains(frequency)) {
      return false;
    }

    return true;
  }

  /// Verify that scheduling was successful
  Future<void> _verifySchedulingSuccess(
      String frequency, DateTime startDateTime) async {
    try {
      // Log scheduling verification
      Logger.info(
          'Verifying $frequency reminder scheduling for ${startDateTime.toIso8601String()}');

      // For now, just log success - in future could check if notification was actually scheduled
      Logger.info('Reminder scheduling verification completed');
    } catch (e) {
      Logger.warning('Could not verify scheduling success: $e');
    }
  }

  /// Attempt fallback scheduling if primary scheduling fails
  Future<void> _attemptFallbackScheduling() async {
    try {
      Logger.info('Attempting fallback reminder scheduling');

      // Could implement alternative scheduling methods here
      // For now, just log the attempt
      Logger.warning('Fallback scheduling not yet implemented');
    } catch (e) {
      Logger.error('Fallback scheduling also failed: $e');
    }
  }

  /// Cancel all scheduled reminders
  Future<void> cancelAllReminders() async {
    try {
      final scheduledIds = await _getScheduledReminderIds();

      if (scheduledIds.isNotEmpty) {
        final notificationService =
            await serviceLocator.getAsync<NotificationService>();

        for (final id in scheduledIds) {
          try {
            await notificationService.cancelNotification(id);
          } catch (e) {
            Logger.error('Failed to cancel reminder $id: $e');
          }
        }

        // Clear stored IDs
        await _clearScheduledReminderIds();

        Logger.info('Cancelled ${scheduledIds.length} scheduled reminders');
      }
    } catch (e) {
      Logger.error('Error cancelling reminders: $e');
    }
  }

  /// Update reminders when settings change
  Future<void> updateReminders() async {
    await scheduleReminders();
  }

  /// Handle reminder firing and auto-reschedule next one
  Future<void> onReminderFired() async {
    try {
      await _setLastNotificationDate(NotificationType.readingReminder);
      Logger.info(
          'ReminderSchedulingService: Reminder fired, auto-rescheduling next one');

      // Auto-dismiss current reminder by clearing it from app notifications
      await _dismissCurrentReminder();

      // Schedule the next reminder
      await scheduleReminders();

      Logger.info(
          'ReminderSchedulingService: Successfully auto-rescheduled next reminder');
    } catch (e) {
      Logger.error(
          'ReminderSchedulingService: Failed to auto-reschedule reminder: $e');
    }
  }

  /// Dismiss current reminder from app notifications
  Future<void> _dismissCurrentReminder() async {
    try {
      // Get notification repository directly from service locator
      final notificationRepository =
          await serviceLocator.getAsync<NotificationRepository>();
      await notificationRepository
          .deleteNotificationsByType(NotificationType.readingReminder);

      Logger.info(
          'ReminderSchedulingService: Dismissed current reminder from app notifications');
    } catch (e) {
      Logger.error(
          'ReminderSchedulingService: Failed to dismiss current reminder: $e');
    }
  }

  /// Check if reminders should be active based on settings
  Future<bool> shouldRemindersBeActive() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final remindersEnabled =
          prefs.getBool(PreferenceKeys.remindersEnabled) ?? false;
      final startDateTime =
          prefs.getString(PreferenceKeys.reminderStartDateTime);

      return remindersEnabled && startDateTime != null;
    } catch (e) {
      Logger.error('Error checking reminder status: $e');
      return false;
    }
  }

  /// Get next few reminder dates for UI display
  Future<List<DateTime>> getUpcomingReminders({int count = 3}) async {
    try {
      final settings = await _loadReminderSettings();

      if (!settings['enabled'] || settings['startDateTime'] == null) {
        return [];
      }

      return ReminderCalculator.calculateNextReminders(
        startDateTime: settings['startDateTime'],
        frequency: settings['frequency'],
        count: count,
      );
    } catch (e) {
      Logger.error('Error getting upcoming reminders: $e');
      return [];
    }
  }

  /// Load reminder settings from preferences
  Future<Map<String, dynamic>> _loadReminderSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final enabled = prefs.getBool(PreferenceKeys.remindersEnabled) ?? false;
      final frequency =
          prefs.getString(PreferenceKeys.reminderFrequency) ?? 'weekly';
      final startDateTimeString =
          prefs.getString(PreferenceKeys.reminderStartDateTime);

      DateTime? startDateTime;
      if (startDateTimeString != null) {
        try {
          startDateTime = DateTime.parse(startDateTimeString);
        } catch (e) {
          Logger.error('Error parsing reminder start date: $e');
        }
      }

      return {
        'enabled': enabled,
        'frequency': frequency,
        'startDateTime': startDateTime,
      };
    } catch (e) {
      Logger.error('Error loading reminder settings: $e');
      return {
        'enabled': false,
        'frequency': 'weekly',
        'startDateTime': null,
      };
    }
  }

  /// Get scheduled reminder IDs
  Future<List<int>> _getScheduledReminderIds() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final idsString = prefs.getString(_scheduledRemindersKey);

      if (idsString == null || idsString.isEmpty) return [];

      return idsString
          .split(',')
          .where((s) => s.isNotEmpty)
          .map((s) => int.tryParse(s))
          .where((id) => id != null)
          .cast<int>()
          .toList();
    } catch (e) {
      Logger.error('Error getting scheduled reminder IDs: $e');
      return [];
    }
  }

  /// Clear scheduled reminder IDs
  Future<void> _clearScheduledReminderIds() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_scheduledRemindersKey);
    } catch (e) {
      Logger.error('Error clearing scheduled reminder IDs: $e');
    }
  }

  /// Check if reminder should be skipped due to recent reading (after 3 AM today)
  Future<bool> _shouldSkipReminderDueToRecentReading(
      DateTime reminderDate) async {
    try {
      final meterReadingRepo = serviceLocator<MeterReadingRepository>();
      final allReadings = await meterReadingRepo.getAllMeterReadings();

      final now = DateTime.now();
      final today3AM = DateTime(now.year, now.month, now.day, 3, 0, 0);

      // Check if any reading exists after 3 AM today
      final hasRecentReading =
          allReadings.any((reading) => reading.date.isAfter(today3AM));

      Logger.info(
          'ReminderSchedulingService: Reading after 3 AM today: $hasRecentReading');
      return hasRecentReading;
    } catch (e) {
      Logger.error(
          'ReminderSchedulingService: Error checking recent readings: $e');
      return false; // Default to false (don't skip) if error
    }
  }

  /// Check if a meter reading reminder should be fired now and schedule it if needed
  Future<void> checkAndScheduleReminders() async {
    try {
      Logger.info(
          'ReminderSchedulingService: Checking if meter reading reminder should be fired');

      final settings = await _loadReminderSettings();
      if (!settings['enabled'] || settings['startDateTime'] == null) {
        Logger.info('Meter reading reminders are not enabled');
        return;
      }

      // Get next scheduled reminder time
      final nextReminderDates = ReminderCalculator.calculateNextReminders(
        startDateTime: settings['startDateTime'],
        frequency: settings['frequency'],
        count: 1,
      );

      if (nextReminderDates.isEmpty) {
        Logger.info('No reminder dates calculated');
        return;
      }

      final nextReminder = nextReminderDates.first;
      final now = DateTime.now();

      Logger.info(
          'Next reminder time: ${nextReminder.toString()}, Current time: ${now.toString()}');

      // Check if we should fire a reminder (within 15 minutes window)
      final timeUntilReminder = nextReminder.difference(now);
      final timeSinceReminder = now.difference(nextReminder);

      // If reminder was supposed to fire within the last 15 minutes OR
      // should fire within the next 15 minutes
      if ((timeSinceReminder.inMinutes <= 15 &&
              !timeSinceReminder.isNegative) ||
          timeUntilReminder.inMinutes <= 15) {
        // Check if we should skip due to recent reading
        if (await _shouldSkipReminderDueToRecentReading(nextReminder)) {
          Logger.info(
              'Skipping reminder - reading already taken after 3 AM today');
          // Schedule next reminder
          await scheduleReminders();
          return;
        }

        // Check deduplication
        if (await _wasNotificationSentToday(NotificationType.readingReminder)) {
          Logger.info('Skipping reminder - already sent today');
          // Schedule next reminder
          await scheduleReminders();
          return;
        }

        Logger.info('Firing meter reading reminder');
        final contentGenerator = ReminderContentGenerator();
        final content = await contentGenerator.generateContextualContent(
          settings['frequency'],
        );

        final notification = AppNotification(
          title: content['title']!,
          message: content['message']!,
          timestamp: now,
          type: NotificationType.readingReminder,
        );

        final notificationService =
            await serviceLocator.getAsync<NotificationService>();
        await notificationService.showNotification(notification);

        // Store that we've sent this reminder
        await _setLastNotificationDate(NotificationType.readingReminder);

        // Schedule next reminder
        await scheduleReminders();
      } else {
        Logger.info('No reminder needed at this time');
      }
    } catch (e) {
      Logger.error('Error checking meter reading reminders: $e');
    }
  }

  /// Check if notification was sent today (deduplication)
  Future<bool> _wasNotificationSentToday(NotificationType type) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = _getNotificationDateKey(type);
      final lastDateString = prefs.getString(key);

      if (lastDateString == null) return false;

      final lastDate = DateTime.parse(lastDateString);
      final today = DateTime.now();

      return lastDate.year == today.year &&
          lastDate.month == today.month &&
          lastDate.day == today.day;
    } catch (e) {
      Logger.error(
          'ReminderSchedulingService: Error checking notification date: $e');
      return false;
    }
  }

  /// Set last notification date for deduplication
  Future<void> _setLastNotificationDate(NotificationType type) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = _getNotificationDateKey(type);
      await prefs.setString(key, DateTime.now().toIso8601String());
    } catch (e) {
      Logger.error(
          'ReminderSchedulingService: Error setting notification date: $e');
    }
  }

  /// Get preference key for notification date tracking
  String _getNotificationDateKey(NotificationType type) {
    switch (type) {
      case NotificationType.readingReminder:
        return 'last_reading_reminder_notification_date';
      default:
        return 'last_notification_date_${type.toString()}';
    }
  }
}
