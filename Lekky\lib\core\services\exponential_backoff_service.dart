import 'dart:math';
import '../utils/logger.dart';

/// Service for managing exponential backoff retry strategies
class ExponentialBackoffService {
  static const Duration _initialDelay = Duration(seconds: 1);
  static const Duration _maxDelay = Duration(seconds: 300); // 5 minutes
  static const double _backoffMultiplier = 2.0;
  static const int _maxRetryAttempts = 5;

  /// Calculate delay for retry attempt
  static Duration calculateDelay(int attemptNumber) {
    if (attemptNumber <= 0) return Duration.zero;
    
    final delayMs = _initialDelay.inMilliseconds * 
        pow(_backoffMultiplier, attemptNumber - 1);
    
    final cappedDelayMs = min(delayMs.toInt(), _maxDelay.inMilliseconds);
    return Duration(milliseconds: cappedDelayMs);
  }

  /// Execute operation with exponential backoff retry
  static Future<T?> executeWithBackoff<T>({
    required Future<T> Function() operation,
    required String operationName,
    int maxAttempts = _maxRetryAttempts,
    bool Function(dynamic error)? shouldRetry,
  }) async {
    dynamic lastError;
    
    for (int attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        Logger.info('$operationName: Attempt $attempt/$maxAttempts');
        final result = await operation();
        
        if (attempt > 1) {
          Logger.info('$operationName: Succeeded on attempt $attempt');
        }
        
        return result;
      } catch (error) {
        lastError = error;
        Logger.warning('$operationName: Attempt $attempt failed: $error');
        
        // Check if we should retry this error
        if (shouldRetry != null && !shouldRetry(error)) {
          Logger.info('$operationName: Error not retryable, stopping');
          break;
        }
        
        // Don't delay after the last attempt
        if (attempt < maxAttempts) {
          final delay = calculateDelay(attempt);
          Logger.info('$operationName: Waiting ${delay.inSeconds}s before retry');
          await Future.delayed(delay);
        }
      }
    }
    
    Logger.error('$operationName: All $maxAttempts attempts failed. Last error: $lastError');
    return null;
  }

  /// Check if error is retryable
  static bool isRetryableError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    
    // Network-related errors are retryable
    if (errorString.contains('network') ||
        errorString.contains('timeout') ||
        errorString.contains('connection') ||
        errorString.contains('socket')) {
      return true;
    }
    
    // System resource errors are retryable
    if (errorString.contains('resource') ||
        errorString.contains('busy') ||
        errorString.contains('unavailable')) {
      return true;
    }
    
    // Permission errors are not retryable
    if (errorString.contains('permission') ||
        errorString.contains('denied') ||
        errorString.contains('unauthorized')) {
      return false;
    }
    
    // Default to retryable for unknown errors
    return true;
  }

  /// Get human-readable delay description
  static String getDelayDescription(int attemptNumber) {
    final delay = calculateDelay(attemptNumber);
    if (delay.inMinutes > 0) {
      return '${delay.inMinutes}m ${delay.inSeconds % 60}s';
    }
    return '${delay.inSeconds}s';
  }
}
