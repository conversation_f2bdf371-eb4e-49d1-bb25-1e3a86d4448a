// File: lib/core/navigation/navigation_service.dart
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../utils/logger.dart';
import '../routing/app_router.dart';
import '../constants/app_constants.dart';
import 'app_routes.dart';
import 'navigation_analytics_service.dart';

/// Centralized navigation service with type safety and analytics
class NavigationService {
  final GoRouter _router;
  final NavigationAnalyticsService _analytics;

  NavigationService(this._router, this._analytics);

  /// Navigate to a typed route
  Future<T?> navigateTo<T>(AppRoute route) async {
    try {
      // Track analytics
      await _analytics.trackNavigation(route);

      // Perform navigation using context.push equivalent
      _router.push(route.path);

      Logger.info('Navigation completed: ${route.path}');
      return null;
    } catch (e, stackTrace) {
      _handleNavigationError(e, stackTrace, route);
      return null;
    }
  }

  /// Navigate and replace current route
  Future<T?> navigateToReplacement<T>(AppRoute route) async {
    try {
      await _analytics.trackNavigation(route, isReplacement: true);

      // Use go for replacement navigation
      _router.go(route.path);

      Logger.info('Navigation replacement: ${route.path}');
      return null; // go() doesn't return a value
    } catch (e, stackTrace) {
      _handleNavigationError(e, stackTrace, route);
      return null;
    }
  }

  /// Go to route (declarative navigation)
  void goTo(AppRoute route) {
    try {
      _analytics.trackNavigation(route);
      _router.go(route.path);

      Logger.info('Navigation (go): ${route.path}');
    } catch (e, stackTrace) {
      _handleNavigationError(e, stackTrace, route);
    }
  }

  /// Navigate back
  void goBack() {
    try {
      if (_router.canPop()) {
        _analytics.trackBackNavigation();
        _router.pop();
        Logger.info('Navigation back completed');
      } else {
        Logger.warning('Cannot navigate back - no routes in stack');
      }
    } catch (e, stackTrace) {
      Logger.error('Navigation back failed: $e', stackTrace);
    }
  }

  /// Navigate back with result
  void goBackWithResult<T>(T result) {
    try {
      if (_router.canPop()) {
        _analytics.trackBackNavigation();
        _router.pop(result);
        Logger.info('Navigation back with result completed');
      }
    } catch (e, stackTrace) {
      Logger.error('Navigation back with result failed: $e', stackTrace);
    }
  }

  /// Handle fallback navigation for main tabs
  void handleFallbackNavigation(String currentLocation) {
    try {
      // Check if user is on a main tab that should fallback to home
      if (currentLocation == AppConstants.routeMainSettings ||
          currentLocation == AppConstants.routeCost ||
          currentLocation == AppConstants.routeHistory) {
        _analytics.trackBackNavigation();
        _router.go(AppConstants.routeHome);
        Logger.info(
            'Fallback navigation to home completed from: $currentLocation');
      } else {
        Logger.info('No fallback navigation needed for: $currentLocation');
      }
    } catch (e, stackTrace) {
      Logger.error('Fallback navigation failed: $e', stackTrace);
    }
  }

  /// Check if current location is home tab
  bool isOnHomeTab(String currentLocation) {
    return currentLocation == AppConstants.routeHome;
  }

  /// Check if can navigate back
  bool canGoBack() => _router.canPop();

  /// Get current route information
  String get currentLocation =>
      _router.routeInformationProvider.value.uri.toString();

  /// Clear navigation history and go to route
  void clearAndGoTo(AppRoute route) {
    try {
      _analytics.trackNavigation(route, isClearAndGo: true);

      // Clear history by going to route
      _router.go(route.path);

      Logger.info('Clear and go navigation: ${route.path}');
    } catch (e, stackTrace) {
      _handleNavigationError(e, stackTrace, route);
    }
  }

  /// Handle navigation errors
  void _handleNavigationError(
    Object error,
    StackTrace stackTrace,
    AppRoute route,
  ) {
    Logger.error('Navigation failed: ${route.path} - $error', stackTrace);

    // Track error in analytics
    _analytics.trackNavigationError(error, route);
  }
}

/// Navigation exception
class NavigationException implements Exception {
  final String message;
  NavigationException(this.message);

  @override
  String toString() => 'NavigationException: $message';
}

/// Navigation service provider
final navigationServiceProvider = Provider<NavigationService>((ref) {
  final router = ref.watch(appRouterProvider);
  final analytics = ref.watch(navigationAnalyticsServiceProvider);
  return NavigationService(router, analytics);
});
