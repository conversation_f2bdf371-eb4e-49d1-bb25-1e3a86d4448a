import 'package:intl/intl.dart' as intl;
import '../models/settings_state.dart';

/// Service for formatting dates according to user preferences
class DateFormatterService {
  final SettingsState settings;

  const DateFormatterService(this.settings);

  /// Create with default settings for fallback scenarios
  factory DateFormatterService.withDefaults() {
    return const DateFormatterService(
      SettingsState(
        dateFormat: 'DD-MM-YYYY',
        showTimeWithDate: false,
      ),
    );
  }

  /// Get the intl DateFormat pattern from settings
  String get _datePattern {
    switch (settings.dateFormat.toUpperCase()) {
      case 'MM-DD-YYYY':
        return 'MM-dd-yyyy';
      case 'YYYY-MM-DD':
        return 'yyyy-MM-dd';
      case 'DD-MM-YYYY':
      default:
        return 'dd-MM-yyyy';
    }
  }

  /// Format a date for history table entries
  String formatDateForHistory(DateTime date) {
    final formattedDate = intl.DateFormat(_datePattern).format(date);

    if (settings.showTimeWithDate) {
      final formattedTime = intl.DateFormat('HH:mm').format(date);
      return '$formattedDate $formattedTime';
    }

    return formattedDate;
  }

  /// Format a date for dashboard components
  String formatDateForDashboard(DateTime date) {
    final formattedDate = intl.DateFormat(_datePattern).format(date);

    if (settings.showTimeWithDate) {
      final formattedTime = intl.DateFormat('HH:mm').format(date);
      return '$formattedDate $formattedTime';
    }

    return formattedDate;
  }

  /// Format a date for recent activity entries
  String formatDateForRecentActivity(DateTime date) {
    final formattedDate = intl.DateFormat(_datePattern).format(date);

    if (settings.showTimeWithDate) {
      final formattedTime = intl.DateFormat('HH:mm').format(date);
      return '$formattedDate $formattedTime';
    }

    return formattedDate;
  }

  /// Format a date for validation dialogs
  String formatDateForValidation(DateTime date) {
    final formattedDate = intl.DateFormat(_datePattern).format(date);

    if (settings.showTimeWithDate) {
      final formattedTime = intl.DateFormat('HH:mm').format(date);
      return '$formattedDate $formattedTime';
    }

    return formattedDate;
  }

  /// Format a date for charts
  String formatDateForChart(DateTime date) {
    // Charts use compact format, respect date format but not time
    switch (settings.dateFormat.toUpperCase()) {
      case 'MM-DD-YYYY':
        return intl.DateFormat('MM/dd').format(date);
      case 'YYYY-MM-DD':
        return intl.DateFormat('MM-dd').format(date);
      case 'DD-MM-YYYY':
      default:
        return intl.DateFormat('dd/MM').format(date);
    }
  }

  /// Format a date for charts with year
  String formatDateForChartWithYear(DateTime date) {
    switch (settings.dateFormat.toUpperCase()) {
      case 'MM-DD-YYYY':
        return intl.DateFormat('MM/dd/yyyy').format(date);
      case 'YYYY-MM-DD':
        return intl.DateFormat('yyyy/MM/dd').format(date);
      case 'DD-MM-YYYY':
      default:
        return intl.DateFormat('dd/MM/yyyy').format(date);
    }
  }

  /// Format a date for cost date range picker
  String formatDateForCostRange(DateTime date) {
    final formattedDate = intl.DateFormat(_datePattern).format(date);

    if (settings.showTimeWithDate) {
      final formattedTime = intl.DateFormat('HH:mm').format(date);
      return '$formattedDate $formattedTime';
    }

    return formattedDate;
  }

  /// Format a date with time for entry dialogs (always shows time)
  String formatDateTimeForEntry(DateTime date) {
    final formattedDate = intl.DateFormat(_datePattern).format(date);
    final formattedTime = intl.DateFormat('HH:mm').format(date);
    return '$formattedDate $formattedTime';
  }

  /// Format a date for meter info card (always shows time)
  String formatDateForMeterInfo(DateTime date) {
    return intl.DateFormat('dd MMM yyyy, HH:mm').format(date);
  }

  /// Format a compact date for dashboard (no time)
  String formatDateForDashboardCompact(DateTime date) {
    return intl.DateFormat('dd MMM').format(date);
  }
}
