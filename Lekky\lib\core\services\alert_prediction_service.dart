import 'package:shared_preferences/shared_preferences.dart';
import '../di/service_locator.dart';
import '../utils/logger.dart';
import '../constants/preference_keys.dart';

import '../../features/notifications/data/notification_service.dart';

/// Service for predicting when alerts will trigger and pre-scheduling system notifications
class AlertPredictionService {
  static final AlertPredictionService _instance =
      AlertPredictionService._internal();

  factory AlertPredictionService() => _instance;
  AlertPredictionService._internal();

  static const String _scheduledAlertIdsKey = 'scheduled_alert_ids';

  /// Predict and schedule future alerts based on current usage patterns
  Future<void> predictAndScheduleAlerts() async {
    try {
      final settings = await _loadAlertSettings();

      if (!settings['notificationsEnabled']) {
        Logger.info('Notifications disabled, skipping alert prediction');
        return;
      }

      // Cancel existing scheduled alerts
      await _cancelScheduledAlerts();

      // Note: AlertCoordinationService was removed - this service is deprecated
      Logger.info(
          'AlertPredictionService: Service temporarily disabled due to architecture changes');
      return;
    } catch (e) {
      Logger.error('Error predicting and scheduling alerts: $e');
    }
  }

  /// Cancel all scheduled alerts
  Future<void> _cancelScheduledAlerts() async {
    try {
      final scheduledIds = await _getScheduledAlertIds();

      if (scheduledIds.isNotEmpty) {
        final notificationService =
            await serviceLocator.getAsync<NotificationService>();

        for (final id in scheduledIds) {
          try {
            await notificationService.cancelNotification(id);
          } catch (e) {
            Logger.error('Failed to cancel scheduled alert $id: $e');
          }
        }

        await _clearScheduledAlertIds();
        Logger.info('Cancelled ${scheduledIds.length} scheduled alerts');
      }
    } catch (e) {
      Logger.error('Error cancelling scheduled alerts: $e');
    }
  }

  /// Load alert settings from preferences
  Future<Map<String, dynamic>> _loadAlertSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      return {
        'notificationsEnabled': (prefs
                    .getBool(PreferenceKeys.lowBalanceAlertsEnabled) ??
                false) ||
            (prefs.getBool(PreferenceKeys.timeToTopUpAlertsEnabled) ?? false) ||
            (prefs.getBool(PreferenceKeys.invalidRecordAlertsEnabled) ?? false),
        'lowBalanceEnabled':
            prefs.getBool(PreferenceKeys.lowBalanceAlertsEnabled) ?? false,
        'timeToTopUpEnabled':
            prefs.getBool(PreferenceKeys.timeToTopUpAlertsEnabled) ?? false,
        'alertThreshold': prefs.getDouble(PreferenceKeys.alertThreshold) ?? 5.0,
        'daysInAdvance': prefs.getInt(PreferenceKeys.daysInAdvance) ?? 5,
      };
    } catch (e) {
      Logger.error('Error loading alert settings: $e');
      return {
        'notificationsEnabled': false,
        'lowBalanceEnabled': false,
        'timeToTopUpEnabled': false,
        'alertThreshold': 5.0,
        'daysInAdvance': 5,
      };
    }
  }

  /// Get scheduled alert IDs
  Future<List<int>> _getScheduledAlertIds() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final idsString = prefs.getString(_scheduledAlertIdsKey);

      if (idsString == null || idsString.isEmpty) return [];

      return idsString
          .split(',')
          .where((s) => s.isNotEmpty)
          .map((s) => int.tryParse(s))
          .where((id) => id != null)
          .cast<int>()
          .toList();
    } catch (e) {
      Logger.error('Error getting scheduled alert IDs: $e');
      return [];
    }
  }

  /// Clear scheduled alert IDs
  Future<void> _clearScheduledAlertIds() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_scheduledAlertIdsKey);
    } catch (e) {
      Logger.error('Error clearing scheduled alert IDs: $e');
    }
  }
}
