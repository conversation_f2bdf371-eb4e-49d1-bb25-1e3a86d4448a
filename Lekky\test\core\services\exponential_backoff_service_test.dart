import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/core/services/exponential_backoff_service.dart';

void main() {
  group('ExponentialBackoffService', () {
    test('calculateDelay returns correct exponential delays', () {
      // Test initial delay
      expect(ExponentialBackoffService.calculateDelay(1),
          const Duration(seconds: 1));

      // Test exponential growth
      expect(ExponentialBackoffService.calculateDelay(2),
          const Duration(seconds: 2));
      expect(ExponentialBackoffService.calculateDelay(3),
          const Duration(seconds: 4));
      expect(ExponentialBackoffService.calculateDelay(4),
          const Duration(seconds: 8));

      // Test max delay cap
      expect(ExponentialBackoffService.calculateDelay(10),
          const Duration(seconds: 300)); // Should be capped at 5 minutes
    });

    test('calculateDelay handles edge cases', () {
      // Test zero and negative attempts
      expect(ExponentialBackoffService.calculateDelay(0), Duration.zero);
      expect(ExponentialBackoffService.calculateDelay(-1), Duration.zero);
    });

    test('isRetryableError correctly identifies retryable errors', () {
      // Network errors should be retryable
      expect(
          ExponentialBackoffService.isRetryableError(
              Exception('Network connection failed')),
          true);
      expect(
          ExponentialBackoffService.isRetryableError(
              Exception('Socket timeout')),
          true);
      expect(
          ExponentialBackoffService.isRetryableError(
              Exception('Connection refused')),
          true);

      // Resource errors should be retryable
      expect(
          ExponentialBackoffService.isRetryableError(
              Exception('Resource busy')),
          true);
      expect(
          ExponentialBackoffService.isRetryableError(
              Exception('Service unavailable')),
          true);

      // Permission errors should not be retryable
      expect(
          ExponentialBackoffService.isRetryableError(
              Exception('Permission denied')),
          false);
      expect(
          ExponentialBackoffService.isRetryableError(
              Exception('Unauthorized access')),
          false);

      // Unknown errors should be retryable by default
      expect(
          ExponentialBackoffService.isRetryableError(
              Exception('Unknown error')),
          true);
    });

    test('executeWithBackoff succeeds on first attempt', () async {
      int attemptCount = 0;

      final result = await ExponentialBackoffService.executeWithBackoff<String>(
        operation: () async {
          attemptCount++;
          return 'success';
        },
        operationName: 'TestOperation',
      );

      expect(result, 'success');
      expect(attemptCount, 1);
    });

    test('executeWithBackoff retries on failure and eventually succeeds',
        () async {
      int attemptCount = 0;

      final result = await ExponentialBackoffService.executeWithBackoff<String>(
        operation: () async {
          attemptCount++;
          if (attemptCount < 3) {
            throw Exception('Network timeout');
          }
          return 'success';
        },
        operationName: 'TestOperation',
        maxAttempts: 5,
      );

      expect(result, 'success');
      expect(attemptCount, 3);
    });

    test('executeWithBackoff returns null after max attempts', () async {
      int attemptCount = 0;

      final result = await ExponentialBackoffService.executeWithBackoff<String>(
        operation: () async {
          attemptCount++;
          throw Exception('Persistent failure');
        },
        operationName: 'TestOperation',
        maxAttempts: 3,
      );

      expect(result, null);
      expect(attemptCount, 3);
    });

    test('executeWithBackoff respects shouldRetry function', () async {
      int attemptCount = 0;

      final result = await ExponentialBackoffService.executeWithBackoff<String>(
        operation: () async {
          attemptCount++;
          throw Exception('Permission denied');
        },
        operationName: 'TestOperation',
        maxAttempts: 5,
        shouldRetry: (error) => !error.toString().contains('Permission'),
      );

      expect(result, null);
      expect(attemptCount, 1); // Should stop after first attempt
    });

    test('getDelayDescription returns human-readable format', () {
      expect(ExponentialBackoffService.getDelayDescription(1), '1s');
      expect(ExponentialBackoffService.getDelayDescription(2), '2s');
      expect(
          ExponentialBackoffService.getDelayDescription(6), '32s'); // 2^5 = 32
      expect(ExponentialBackoffService.getDelayDescription(10),
          '5m 0s'); // Capped at 300s
    });
  });
}
