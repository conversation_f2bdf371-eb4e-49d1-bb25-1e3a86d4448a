import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/app_banner.dart';

/// Base class for settings screens providing consistent navigation behavior
abstract class BaseSettingsScreen extends ConsumerWidget {
  /// Screen title for the banner
  final String title;

  /// Category index for back navigation
  final int categoryIndex;

  /// Whether to show haptic feedback on navigation
  final bool enableHapticFeedback;

  /// Constructor
  const BaseSettingsScreen({
    super.key,
    required this.title,
    required this.categoryIndex,
    this.enableHapticFeedback = true,
  });

  /// Build the main content of the screen
  Widget buildContent(BuildContext context, WidgetRef ref);

  /// Handle back navigation with haptic feedback
  void _handleBackNavigation(BuildContext context) {
    if (enableHapticFeedback) {
      HapticFeedback.lightImpact();
    }
    context.go('/main-settings?expanded=$categoryIndex');
  }

  /// Show success message with haptic feedback
  void showSuccessMessage(BuildContext context, String message) {
    if (enableHapticFeedback) {
      HapticFeedback.selectionClick();
    }
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  /// Show error message with haptic feedback
  void showErrorMessage(BuildContext context, String message) {
    if (enableHapticFeedback) {
      HapticFeedback.heavyImpact();
    }
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) async {
        if (didPop) return;
        _handleBackNavigation(context);
      },
      child: Scaffold(
        body: Column(
          children: [
            // Consistent banner with back navigation
            GestureDetector(
              onTap: () => _handleBackNavigation(context),
              child: AppBanner(
                message: '← $title',
                gradientColors: AppColors.getSettingsMainCardGradient(
                    Theme.of(context).brightness == Brightness.dark),
                textColor: AppColors.getAppBarTextColor('settings',
                    Theme.of(context).brightness == Brightness.dark),
              ),
            ),
            // Screen content
            Expanded(
              child: buildContent(context, ref),
            ),
          ],
        ),
      ),
    );
  }
}
