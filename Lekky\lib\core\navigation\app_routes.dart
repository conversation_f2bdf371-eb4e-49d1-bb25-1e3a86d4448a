// File: lib/core/navigation/app_routes.dart
import '../constants/app_constants.dart';

/// Enum representing all possible routes in the app
enum AppRoute {
  // Main Routes
  splash,
  welcome,
  setup,
  home,
  validationDashboard,

  // Main Tab Routes
  history,
  cost,
  mainSettings,

  // Standalone Routes
  settings,

  // CSV Routes
  csvExport,
  csvImport,

  // Data Routes
  deleteAllData,

  // About Routes
  about,
  aboutInfo,
  update,
  tipsTricks,

  // Donate Routes
  donate,
  donateOptions,

  // Appearance Routes
  appearance,
  theme,

  // Region Routes
  region,
  language,
  currency,

  // Date Routes
  date,
  dateFormat,
  timeDisplay,

  // Notification Routes
  notifications,
  alertThreshold,
  daysAdvance,
  notificationTypes,
  reminders,
  notificationUtilities,

  // Debug Routes
  debugNotifications;
}

/// Extension to convert AppRoute to path string
extension AppRouteExtension on AppRoute {
  String get path {
    switch (this) {
      case AppRoute.splash:
        return AppConstants.routeSplash;
      case AppRoute.welcome:
        return AppConstants.routeWelcome;
      case AppRoute.setup:
        return AppConstants.routeSetup;
      case AppRoute.home:
        return AppConstants.routeHome;
      case AppRoute.validationDashboard:
        return AppConstants.routeValidationDashboard;
      case AppRoute.history:
        return AppConstants.routeHistory;
      case AppRoute.cost:
        return AppConstants.routeCost;
      case AppRoute.mainSettings:
        return AppConstants.routeMainSettings;
      case AppRoute.settings:
        return '/settings';
      case AppRoute.csvExport:
        return '/main-settings/csv/export';
      case AppRoute.csvImport:
        return '/main-settings/csv/import';
      case AppRoute.deleteAllData:
        return '/main-settings/data/delete-all';
      case AppRoute.about:
        return '/main-settings/app-information';
      case AppRoute.aboutInfo:
        return '/main-settings/app-information';
      case AppRoute.update:
        return '/main-settings/update';
      case AppRoute.tipsTricks:
        return '/main-settings/tips-tricks';
      case AppRoute.donate:
        return '/main-settings/donate';
      case AppRoute.donateOptions:
        return '/main-settings/donate';
      case AppRoute.appearance:
        return '/main-settings/theme';
      case AppRoute.theme:
        return '/main-settings/theme';
      case AppRoute.region:
        return '/main-settings/language';
      case AppRoute.language:
        return '/main-settings/language';
      case AppRoute.currency:
        return '/main-settings/currency';
      case AppRoute.date:
        return '/main-settings/date-format';
      case AppRoute.dateFormat:
        return '/main-settings/date-format';
      case AppRoute.timeDisplay:
        return '/main-settings/time-display';
      case AppRoute.notifications:
        return '/main-settings/notification-types';
      case AppRoute.alertThreshold:
        return '/main-settings/alert-threshold';
      case AppRoute.daysAdvance:
        return '/main-settings/days-advance';
      case AppRoute.notificationTypes:
        return '/main-settings/notification-types';
      case AppRoute.reminders:
        return '/main-settings/reminders';
      case AppRoute.notificationUtilities:
        return '/main-settings/notification-utilities';
      case AppRoute.debugNotifications:
        return AppConstants.routeDebugNotifications;
    }
  }

  /// Get route name for go_router
  String get name {
    switch (this) {
      case AppRoute.splash:
        return 'splash';
      case AppRoute.welcome:
        return 'welcome';
      case AppRoute.setup:
        return 'setup';
      case AppRoute.home:
        return 'home';
      case AppRoute.validationDashboard:
        return 'validation-dashboard';
      case AppRoute.history:
        return 'history';
      case AppRoute.cost:
        return 'cost';
      case AppRoute.mainSettings:
        return 'main-settings';
      case AppRoute.settings:
        return 'settings';
      case AppRoute.csvExport:
        return 'main-csv-export';
      case AppRoute.csvImport:
        return 'main-csv-import';
      case AppRoute.deleteAllData:
        return 'main-delete-all-data';
      case AppRoute.about:
        return 'main-app-information';
      case AppRoute.aboutInfo:
        return 'main-app-information';
      case AppRoute.update:
        return 'main-update';
      case AppRoute.tipsTricks:
        return 'main-tips-tricks';
      case AppRoute.donate:
        return 'main-donate';
      case AppRoute.donateOptions:
        return 'main-donate';
      case AppRoute.appearance:
        return 'main-theme';
      case AppRoute.theme:
        return 'main-theme';
      case AppRoute.region:
        return 'main-language';
      case AppRoute.language:
        return 'main-language';
      case AppRoute.currency:
        return 'main-currency';
      case AppRoute.date:
        return 'main-date-format';
      case AppRoute.dateFormat:
        return 'main-date-format';
      case AppRoute.timeDisplay:
        return 'main-time-display';
      case AppRoute.notifications:
        return 'main-notification-types';
      case AppRoute.alertThreshold:
        return 'main-alert-threshold';
      case AppRoute.daysAdvance:
        return 'main-days-advance';
      case AppRoute.notificationTypes:
        return 'main-notification-types';
      case AppRoute.reminders:
        return 'main-reminders';
      case AppRoute.notificationUtilities:
        return 'main-notification-utilities';
      case AppRoute.debugNotifications:
        return 'debug-notifications';
    }
  }
}

/// Route validation utilities
class RouteValidator {
  /// Validate route parameters
  static bool validateRoute(AppRoute route) {
    // All routes are valid for now
    // Add specific validation logic as needed
    return true;
  }
}
