// File: lib/core/utils/permission_helper.dart
import 'dart:io';
import 'package:permission_handler/permission_handler.dart';
import 'logger.dart';

/// Helper class for handling permissions
class PermissionHelper {
  final Logger _logger = Logger();

  /// Constructor
  PermissionHelper();

  /// Check and request storage permission
  /// Returns true if permission is granted, false otherwise
  Future<bool> checkAndRequestStoragePermission() async {
    try {
      _logger.i('Checking storage permission...');

      // For Android 13+ (API 33+), we need different permissions
      if (Platform.isAndroid) {
        // For Android 13+ (API 33+), use granular media permissions
        // For file export, we primarily need access to create files via SAF
        // which doesn't require explicit permissions when user selects location

        // Check if we're on Android 13+ by trying the new permissions
        try {
          // Try to check media permissions (Android 13+)
          await Permission.photos.status;
          _logger.i(
              'Android 13+ detected, using Storage Access Framework approach');

          // For SAF (Storage Access Framework), we don't need explicit permissions
          // when the user selects the save location themselves
          return true;
        } catch (e) {
          // Fallback to legacy storage permission for older Android versions
          _logger.i('Using legacy storage permission for older Android');

          PermissionStatus status = await Permission.storage.status;

          if (status.isGranted) {
            _logger.i('Storage permission already granted');
            return true;
          }

          // Request permission
          _logger.i('Requesting storage permission...');
          status = await Permission.storage.request();

          if (status.isGranted) {
            _logger.i('Storage permission granted');
            return true;
          } else if (status.isPermanentlyDenied) {
            _logger.w('Storage permission permanently denied');
            return false;
          } else {
            _logger.w('Storage permission denied: $status');
            return false;
          }
        }
      } else {
        // For iOS, no explicit storage permissions needed for SAF
        _logger.i('iOS detected, no explicit storage permissions needed');
        return true;
      }
    } catch (e) {
      _logger.e('Error checking/requesting storage permission',
          details: e.toString());
      return false;
    }
  }

  /// Check if storage permission is granted
  /// Returns true if permission is granted, false otherwise
  Future<bool> hasStoragePermission() async {
    try {
      final status = await Permission.storage.status;
      return status.isGranted;
    } catch (e) {
      _logger.e('Error checking storage permission', details: e.toString());
      return false;
    }
  }

  /// Open app settings
  Future<bool> openAppSettings() async {
    try {
      return await openAppSettings();
    } catch (e) {
      _logger.e('Error opening app settings', details: e.toString());
      return false;
    }
  }
}
