import 'package:flutter/material.dart';
import '../../../../core/utils/date_time_utils.dart';
import '../../../../core/widgets/animated_battery_icon.dart';

/// A card that displays top-up statistics
class TopUpStatisticsCard extends StatelessWidget {
  /// Days to alert threshold
  final double? daysToAlertThreshold;

  /// Days to meter zero
  final double? daysToMeterZero;

  /// Currency symbol to use
  final String currencySymbol;

  /// Alert threshold value
  final double alertThreshold;

  /// Days in advance setting
  final int daysInAdvance;

  /// Date format preference
  final String dateFormat;

  /// Constructor
  const TopUpStatisticsCard({
    super.key,
    required this.daysToAlertThreshold,
    required this.daysToMeterZero,
    this.currencySymbol = '₦',
    required this.alertThreshold,
    required this.daysInAdvance,
    required this.dateFormat,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with icon and title
            Row(
              children: [
                Icon(
                  Icons.schedule,
                  color: theme.colorScheme.onSurface,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Top Up Statistics',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 10),
            // Adaptive layout based on screen size
            LayoutBuilder(
              builder: (context, constraints) {
                final screenWidth = MediaQuery.of(context).size.width;

                if (screenWidth < 360) {
                  // Small screens: vertical layout
                  return _buildVerticalLayout(context);
                } else {
                  // Medium+ screens: 2x2 grid layout
                  return _buildGridLayout(context);
                }
              },
            ),
            const SizedBox(height: 8),
            Text(
              'Based on your Recent Average usage patterns',
              style: TextStyle(
                fontSize: 12,
                color: theme.colorScheme.onSurface.withOpacity(0.6),
                fontStyle: FontStyle.italic,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Build vertical layout for small screens
  Widget _buildVerticalLayout(BuildContext context) {
    return Column(
      children: [
        // Alert Threshold Timeframe
        _buildStatisticItem(
          context,
          'Alert Threshold Timeframe',
          daysToAlertThreshold,
          Icons.notification_important,
          Colors.orange,
          isThreshold: true,
        ),
        const SizedBox(height: 12),
        // Configuration info
        _buildConfigurationInfo(context),
        const SizedBox(height: 12),
        // Zero Meter Timeframe
        _buildStatisticItem(
          context,
          'Zero Meter Timeframe',
          daysToMeterZero,
          Icons.battery_unknown,
          Theme.of(context).colorScheme.secondary,
          isThreshold: false,
        ),
      ],
    );
  }

  /// Build new layout matching user's preferred design
  Widget _buildGridLayout(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      children: [
        // Alert Threshold Timeframe - Large card
        _buildTimeframeCard(
          context,
          'Alert Threshold Timeframe',
          daysToAlertThreshold,
          Icons.notification_important,
          Colors.orange,
          isThreshold: true,
        ),
        const SizedBox(height: 12),
        // Alert Threshold and Days in Advance side by side
        Row(
          children: [
            Expanded(
              child: _buildConfigItem(
                context,
                'Alert Threshold',
                '$currencySymbol${alertThreshold.toStringAsFixed(2)}',
                Icons.warning,
                Colors.orange,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildConfigItem(
                context,
                'Days in Advance',
                '$daysInAdvance days',
                Icons.calendar_today,
                Colors.orange,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        // Zero Meter Timeframe - Large card
        _buildTimeframeCard(
          context,
          'Zero Meter Timeframe',
          daysToMeterZero,
          Icons.battery_unknown,
          theme.colorScheme.secondary,
          isThreshold: false,
        ),
      ],
    );
  }

  /// Build a large timeframe card with days and date on same line
  Widget _buildTimeframeCard(
    BuildContext context,
    String title,
    double? value,
    IconData icon,
    Color color, {
    required bool isThreshold,
  }) {
    final theme = Theme.of(context);
    final isExceeded = value == -1;

    // Get days and date text
    final daysText = isThreshold
        ? DateTimeUtils.getThresholdDaysText(value, isExceeded: isExceeded)
        : DateTimeUtils.getMeterZeroDaysText(value);

    final dateText = isThreshold
        ? DateTimeUtils.getThresholdDateText(value, dateFormat,
            isExceeded: isExceeded)
        : DateTimeUtils.getMeterZeroDateText(value, dateFormat);

    // Combine days and date with bullet separator
    final combinedText =
        dateText.isNotEmpty ? '$daysText • $dateText' : daysText;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header row with icon and title
          Row(
            children: [
              // Use animated battery icon for Zero Meter, static for others
              !isThreshold
                  ? AnimatedBatteryIcon(
                      daysRemaining: value,
                      color: color,
                      size: 16,
                    )
                  : Icon(
                      icon,
                      color: color,
                      size: 16,
                    ),
              const SizedBox(width: 8),
              Flexible(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: theme.colorScheme.onSurface,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          // Combined days and date text
          Center(
            child: Text(
              combinedText,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  /// Build a single statistic item
  Widget _buildStatisticItem(
    BuildContext context,
    String title,
    double? value,
    IconData icon,
    Color color, {
    required bool isThreshold,
  }) {
    final theme = Theme.of(context);
    final isExceeded = value == -1;

    // Get days and date text separately
    final daysText = isThreshold
        ? DateTimeUtils.getThresholdDaysText(value, isExceeded: isExceeded)
        : DateTimeUtils.getMeterZeroDaysText(value);

    final dateText = isThreshold
        ? DateTimeUtils.getThresholdDateText(value, dateFormat,
            isExceeded: isExceeded)
        : DateTimeUtils.getMeterZeroDateText(value, dateFormat);

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Row 1: Title with icon
          Row(
            children: [
              // Use animated battery icon for Zero Meter Timeframe, static icon for others
              isThreshold
                  ? Icon(
                      icon,
                      color: color,
                      size: 16,
                    )
                  : AnimatedBatteryIcon(
                      daysRemaining: value,
                      color: color,
                      size: 16,
                    ),
              const SizedBox(width: 4),
              Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: theme.colorScheme.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          // Row 2: Days text
          Text(
            daysText,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
          ),
          // Row 3: Date text (if available)
          if (dateText.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              dateText,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Build configuration information section
  Widget _buildConfigurationInfo(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildConfigItem(
            context,
            'Alert Threshold',
            '$currencySymbol${alertThreshold.toStringAsFixed(2)}',
            Icons.warning,
            Colors.orange,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildConfigItem(
            context,
            'Days in Advance',
            '$daysInAdvance days',
            Icons.calendar_today,
            Colors.orange,
          ),
        ),
      ],
    );
  }

  /// Build a single configuration item
  Widget _buildConfigItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(6),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: color,
            size: 12,
          ),
          const SizedBox(width: 4),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w500,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
