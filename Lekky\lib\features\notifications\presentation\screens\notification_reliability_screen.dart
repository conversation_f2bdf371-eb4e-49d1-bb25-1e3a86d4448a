import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/services/android_notification_manager.dart';
import '../../../../core/services/ios_notification_manager.dart';
import '../../../../core/services/background_execution_manager.dart';
import '../../../../core/services/battery_optimization_manager.dart';
import '../../../../core/services/notification_permission_manager.dart';
import '../../../../core/services/notification_queue_manager.dart';
import '../../../../core/services/firebase_messaging_service.dart';
import '../../../../core/services/foreground_service_manager.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/widgets/app_banner.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/di/service_locator.dart';
import '../widgets/permission_status_widget.dart';
import '../../data/notification_service.dart';
import '../../domain/models/notification.dart';

/// Screen for displaying notification system reliability status
class NotificationReliabilityScreen extends ConsumerStatefulWidget {
  const NotificationReliabilityScreen({super.key});

  @override
  ConsumerState<NotificationReliabilityScreen> createState() =>
      _NotificationReliabilityScreenState();
}

class _NotificationReliabilityScreenState
    extends ConsumerState<NotificationReliabilityScreen> {
  bool _isLoading = true;
  Map<String, dynamic> _reliabilityStatus = {};

  @override
  void initState() {
    super.initState();
    _loadReliabilityStatus();
  }

  Future<void> _loadReliabilityStatus() async {
    setState(() => _isLoading = true);

    try {
      final status = <String, dynamic>{};

      // Get platform-specific status
      if (Platform.isAndroid) {
        final androidManager = AndroidNotificationManager();
        status['android'] = await androidManager.getAndroidNotificationStatus();

        final batteryManager = BatteryOptimizationManager();
        status['battery'] = await batteryManager.getBatteryOptimizationStatus();
      } else if (Platform.isIOS) {
        final iosManager = IOSNotificationManager();
        status['ios'] = await iosManager.getIOSNotificationStatus();
      }

      // Get background execution status
      final backgroundManager = BackgroundExecutionManager();
      status['background'] =
          await backgroundManager.getBackgroundExecutionStatus();

      // Get permission status
      final permissionManager = NotificationPermissionManager();
      status['permissions'] = {
        'hasPermission': await permissionManager.hasPermission(),
        'timestamp': DateTime.now().toIso8601String(),
      };

      // Get notification queue status
      final queueManager = NotificationQueueManager();
      status['queue'] = await queueManager.getQueueStatus();

      // Get Firebase messaging status
      final firebaseService = FirebaseMessagingService();
      status['firebase'] = await firebaseService.getStatus();

      // Get foreground service status
      final foregroundService = ForegroundServiceManager();
      status['foregroundService'] = await foregroundService.getServiceStatus();

      setState(() {
        _reliabilityStatus = status;
        _isLoading = false;
      });
    } catch (e) {
      Logger.error('Error loading reliability status: $e');
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) async {
        if (didPop) return;
        context.go('/main-settings/notification-utilities');
      },
      child: Scaffold(
        body: Column(
          children: [
            // Banner with back arrow and theme-aware colors (same as Reminders screen)
            GestureDetector(
              onTap: () => context.go('/main-settings/notification-utilities'),
              child: AppBanner(
                message: '← Notification Reliability',
                gradientColors: AppColors.getSettingsMainCardGradient(
                    Theme.of(context).brightness == Brightness.dark),
                textColor: AppColors.getAppBarTextColor('settings',
                    Theme.of(context).brightness == Brightness.dark),
              ),
            ),
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : RefreshIndicator(
                      onRefresh: _loadReliabilityStatus,
                      child: SingleChildScrollView(
                        physics: const AlwaysScrollableScrollPhysics(),
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildOverallStatus(),
                            const SizedBox(height: 24),
                            _buildPermissionStatus(),
                            const SizedBox(height: 24),
                            _buildPlatformSpecificStatus(),
                            const SizedBox(height: 24),
                            _buildBackgroundExecutionStatus(),
                            const SizedBox(height: 24),
                            _buildFirebaseStatus(),
                            const SizedBox(height: 24),
                            _buildForegroundServiceStatus(),
                            const SizedBox(height: 24),
                            _buildQueueStatus(),
                            const SizedBox(height: 24),
                            _buildActionButtons(),
                            const SizedBox(height: 24),
                            _buildTroubleshootingSection(),
                          ],
                        ),
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOverallStatus() {
    final isReliable = _calculateOverallReliability();

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isReliable ? Icons.check_circle : Icons.warning,
                  color: isReliable ? Colors.green : Colors.orange,
                  size: 32,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Notification System',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      Text(
                        isReliable ? 'Reliable' : 'Needs Attention',
                        style: TextStyle(
                          color: isReliable ? Colors.green : Colors.orange,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              isReliable
                  ? 'Your notification system is properly configured for reliable delivery.'
                  : 'Some settings need attention to ensure reliable notification delivery.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionStatus() {
    final permissionData =
        _reliabilityStatus['permissions'] as Map<String, dynamic>?;
    final hasPermission = permissionData?['hasPermission'] as bool? ?? false;

    return PermissionStatusWidget(
      title: 'Notification Permissions',
      hasPermission: hasPermission,
      onRequestPermission: () async {
        final permissionManager = NotificationPermissionManager();
        await permissionManager.requestPermission(context);
        await _loadReliabilityStatus();
      },
    );
  }

  Widget _buildPlatformSpecificStatus() {
    if (Platform.isAndroid) {
      return _buildAndroidStatus();
    } else if (Platform.isIOS) {
      return _buildIOSStatus();
    } else {
      return const SizedBox.shrink();
    }
  }

  Widget _buildAndroidStatus() {
    final androidData = _reliabilityStatus['android'] as Map<String, dynamic>?;
    final batteryData = _reliabilityStatus['battery'] as Map<String, dynamic>?;

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Android Settings',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12),
            _buildStatusItem(
              'Notification Permission',
              androidData?['notificationPermission']
                      ?.toString()
                      .contains('granted') ??
                  false,
            ),
            _buildStatusItem(
              'Exact Alarm Permission',
              androidData?['exactAlarmPermission']
                      ?.toString()
                      .contains('granted') ??
                  false,
            ),
            _buildStatusItem(
              'Battery Optimization Exemption',
              !(batteryData?['isBatteryOptimized'] as bool? ?? true),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIOSStatus() {
    final iosData = _reliabilityStatus['ios'] as Map<String, dynamic>?;

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'iOS Settings',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12),
            _buildStatusItem(
              'Notification Permissions',
              iosData?['permissionsGranted'] as bool? ?? false,
            ),
            _buildStatusItem(
              'Background App Refresh',
              true, // Cannot be checked programmatically
              subtitle: 'Enable in Settings > General > Background App Refresh',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackgroundExecutionStatus() {
    final backgroundData =
        _reliabilityStatus['background'] as Map<String, dynamic>?;
    final isEnabled =
        backgroundData?['isBackgroundExecutionEnabled'] as bool? ?? false;

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Background Execution',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12),
            _buildStatusItem(
              'Background Execution Enabled',
              isEnabled,
              subtitle: isEnabled
                  ? 'App can run in background for notifications'
                  : 'Background execution may be limited',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusItem(String title, bool isEnabled, {String? subtitle}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Icon(
            isEnabled ? Icons.check_circle : Icons.cancel,
            color: isEnabled ? Colors.green : Colors.red,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title),
                if (subtitle != null)
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFirebaseStatus() {
    final firebaseData =
        _reliabilityStatus['firebase'] as Map<String, dynamic>?;
    final isInitialized = firebaseData?['isInitialized'] as bool? ?? false;
    final isAvailable = firebaseData?['isAvailable'] as bool? ?? false;
    final hasToken = firebaseData?['hasToken'] as bool? ?? false;

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isAvailable ? Icons.cloud_done : Icons.cloud_off,
                  color: isAvailable ? Colors.green : Colors.orange,
                ),
                const SizedBox(width: 8),
                Text(
                  'Firebase Cloud Messaging',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildStatusItem(
              'FCM Initialization',
              isInitialized,
              subtitle: isInitialized
                  ? 'Firebase messaging initialized'
                  : 'Firebase messaging not initialized',
            ),
            const SizedBox(height: 8),
            _buildStatusItem(
              'FCM Availability',
              isAvailable,
              subtitle: isAvailable
                  ? 'Firebase messaging available'
                  : 'Firebase messaging unavailable',
            ),
            const SizedBox(height: 8),
            _buildStatusItem(
              'FCM Token',
              hasToken,
              subtitle:
                  hasToken ? 'FCM token obtained' : 'No FCM token available',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildForegroundServiceStatus() {
    final serviceData =
        _reliabilityStatus['foregroundService'] as Map<String, dynamic>?;
    final isRunning = serviceData?['isRunning'] as bool? ?? false;
    final isAndroid = serviceData?['isAndroid'] as bool? ?? false;

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isAndroid
                      ? (isRunning ? Icons.play_circle : Icons.pause_circle)
                      : Icons.info,
                  color: isAndroid
                      ? (isRunning ? Colors.green : Colors.orange)
                      : Colors.grey,
                ),
                const SizedBox(width: 8),
                Text(
                  'Foreground Service',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (isAndroid) ...[
              _buildStatusItem(
                'Service Status',
                isRunning,
                subtitle: isRunning
                    ? 'Foreground service running for reliable notifications'
                    : 'Foreground service not running',
              ),
              if (!isRunning) ...[
                const SizedBox(height: 12),
                OutlinedButton.icon(
                  onPressed: _startForegroundService,
                  icon: const Icon(Icons.play_arrow),
                  label: const Text('Start Service'),
                ),
              ],
            ] else ...[
              _buildStatusItem(
                'Platform Support',
                false,
                subtitle: 'Foreground service only available on Android',
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildQueueStatus() {
    final queueData = _reliabilityStatus['queue'] as Map<String, dynamic>?;
    final totalPending = queueData?['totalPending'] as int? ?? 0;
    final fallbackCount = queueData?['fallbackQueueCount'] as int? ?? 0;
    final failedScheduledCount =
        queueData?['failedScheduledCount'] as int? ?? 0;
    final hasSingleFallback = queueData?['hasSingleFallback'] as bool? ?? false;

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  totalPending > 0 ? Icons.queue : Icons.check_circle,
                  color: totalPending > 0 ? Colors.orange : Colors.green,
                ),
                const SizedBox(width: 8),
                Text(
                  'Notification Queue',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildStatusItem(
              'Total Pending Notifications',
              totalPending == 0,
              subtitle: totalPending > 0
                  ? '$totalPending notifications waiting for retry'
                  : 'No pending notifications',
            ),
            if (totalPending > 0) ...[
              const SizedBox(height: 8),
              if (hasSingleFallback)
                _buildStatusItem(
                  'Immediate Retry Available',
                  false,
                  subtitle: 'One notification ready for immediate retry',
                ),
              if (fallbackCount > 0)
                _buildStatusItem(
                  'Queued Failed Notifications',
                  false,
                  subtitle: '$fallbackCount notifications in retry queue',
                ),
              if (failedScheduledCount > 0)
                _buildStatusItem(
                  'Failed Scheduled Notifications',
                  false,
                  subtitle:
                      '$failedScheduledCount scheduled notifications failed',
                ),
              const SizedBox(height: 12),
              OutlinedButton.icon(
                onPressed: _processNotificationQueue,
                icon: const Icon(Icons.refresh),
                label: const Text('Process Queue Now'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        ElevatedButton.icon(
          onPressed: _testNotification,
          icon: const Icon(Icons.notifications_active),
          label: const Text('Test Notification'),
        ),
        const SizedBox(height: 8),
        OutlinedButton.icon(
          onPressed: _showTroubleshootingGuide,
          icon: const Icon(Icons.help_outline),
          label: const Text('Troubleshooting Guide'),
        ),
      ],
    );
  }

  Widget _buildTroubleshootingSection() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Tips',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12),
            const Text(
                '• Restart the app after changing notification settings'),
            const Text('• Test with a short reminder interval first'),
            const Text('• Check your device\'s Do Not Disturb settings'),
            const Text('• Ensure the app has all required permissions'),
          ],
        ),
      ),
    );
  }

  bool _calculateOverallReliability() {
    final permissionData =
        _reliabilityStatus['permissions'] as Map<String, dynamic>?;
    final hasPermission = permissionData?['hasPermission'] as bool? ?? false;

    final backgroundData =
        _reliabilityStatus['background'] as Map<String, dynamic>?;
    final backgroundEnabled =
        backgroundData?['isBackgroundExecutionEnabled'] as bool? ?? false;

    return hasPermission && backgroundEnabled;
  }

  Future<void> _testNotification() async {
    setState(() => _isLoading = true);

    try {
      final notificationService =
          await serviceLocator.getAsync<NotificationService>();

      final notification = AppNotification(
        title: 'Notification Test',
        message: 'This is a test notification.',
        timestamp: DateTime.now(),
        type: NotificationType.welcome,
      );

      await notificationService.showNotification(notification);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content:
                Text('Test notification sent! Check your notification panel.'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to send notification: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _startForegroundService() async {
    setState(() => _isLoading = true);

    try {
      final foregroundService = ForegroundServiceManager();
      final success = await foregroundService.startService();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success
                ? 'Foreground service started successfully!'
                : 'Failed to start foreground service'),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );

        // Refresh status to show updated service state
        await _loadReliabilityStatus();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error starting foreground service: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _processNotificationQueue() async {
    setState(() => _isLoading = true);

    try {
      final queueManager = NotificationQueueManager();
      await queueManager.processQueue();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Notification queue processed successfully!'),
            backgroundColor: Colors.green,
          ),
        );

        // Refresh status to show updated queue
        await _loadReliabilityStatus();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to process queue: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _showTroubleshootingGuide() async {
    final backgroundManager = BackgroundExecutionManager();
    await backgroundManager.showTroubleshootingGuide(context);
  }
}
